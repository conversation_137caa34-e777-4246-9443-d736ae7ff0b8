<?xml version="1.0" encoding="UTF-8"?>
ft" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.gexf.net/1.2draft http://www.gexf.net/1.2draft/gexf.xsd" version="1.2">
  <meta lastmodifieddate="2025-06-27">
    <creator>NetworkX 3.4.2</creator>
  </meta>
  <graph defaultedgetype="undirected" mode="static" name="">
    <attributes mode="static" class="edge">
      <attribute id="4" title="description" type="string" />
      <attribute id="5" title="rank" type="long" />
    </attributes>
    <attributes mode="static" class="node">
      <attribute id="0" title="type" type="string" />
      <attribute id="1" title="description" type="string" />
      <attribute id="2" title="degree" type="long" />
      <attribute id="3" title="community_ids" type="string" />
    </attributes>
    <nodes>
      <node id="PYTHON" label="PYTHON">
        <attvalues>
          <attvalue for="0" value="ORGANIZATION" />
          <attvalue for="1" value="Python is a high-level programming language widely used for general-purpose programming, known for its readability and ease of use. In the context provided, Python serves as the language in which example functions and modules are written and executed, particularly for demonstrating concepts such as function argument passing and code for formatting names. Python supports a variety of features, including modules, functions, and multiple import mechanisms such as aliasing and wildcard imports, making it a versatile tool for both teaching and practical application. Throughout the text, Python is referenced as the primary language for illustrating programming concepts and executing example code." />
          <attvalue for="2" value="21" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="GREETER.PY" label="GREETER.PY">
        <attvalues>
          <attvalue for="0" value="ORGANIZATION" />
          <attvalue for="1" value="greeter.py is a Python script file that contains the definition and implementation of the greet_user() function, serving as an example for function creation and usage." />
          <attvalue for="2" value="3" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="GREET_USER" label="GREET_USER">
        <attvalues>
          <attvalue for="0" value="EVENT" />
          <attvalue for="1" value="greet_user is a function defined in Python that prints a greeting message. It can be called with or without a username to display a personalized or generic greeting." />
          <attvalue for="2" value="5" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="USERNAME" label="USERNAME">
        <attvalues>
          <attvalue for="0" value="PERSON" />
          <attvalue for="1" value="username is a parameter in the greet_user function, representing the name of the user to be greeted. It is a placeholder for any person's name passed to the function." />
          <attvalue for="2" value="1" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="MODULE" label="MODULE">
        <attvalues>
          <attvalue for="0" value="ORGANIZATION" />
          <attvalue for="1" value="A module in Python is an independent file containing functions, classes, or variables, which can be imported into the main program to keep the code organized and maintainable." />
          <attvalue for="2" value="3" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="JESSE" label="JESSE">
        <attvalues>
          <attvalue for="0" value="PERSON" />
          <attvalue for="1" value="JESSE is a person whose name is commonly used as an example argument in Python programming, particularly in demonstrations of how to pass values to function parameters. In instructional materials, JESSE often appears as an example username in the function call greet_user('jesse'), where the function is designed to provide a personalized greeting to the user. This usage helps illustrate how functions can accept input and generate customized output based on the provided argument. JESSE, therefore, serves as a representative user in programming examples, making it easier for learners to understand the concept of passing arguments to functions and creating personalized interactions in code." />
          <attvalue for="2" value="2" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="SARAH" label="SARAH">
        <attvalues>
          <attvalue for="0" value="PERSON" />
          <attvalue for="1" value="Sarah is another example username used in the greet_user('sarah') function call, representing a user who receives a personalized greeting." />
          <attvalue for="2" value="1" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="8.1 &#23450;&#20041;&#20989;&#25968;" label="8.1 &#23450;&#20041;&#20989;&#25968;">
        <attvalues>
          <attvalue for="0" value="EVENT" />
          <attvalue for="1" value="Section 8.1, titled &quot;Defining Functions,&quot; is a subsection of Chapter 8 that explains how to define functions in Python, including syntax and structure." />
          <attvalue for="2" value="2" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="8.1.1 &#21521;&#20989;&#25968;&#20256;&#36882;&#20449;&#24687;" label="8.1.1 &#21521;&#20989;&#25968;&#20256;&#36882;&#20449;&#24687;">
        <attvalues>
          <attvalue for="0" value="EVENT" />
          <attvalue for="1" value="Section 8.1.1, &quot;Passing Information to Functions,&quot; is a subsection that discusses how to provide input to functions in Python through parameters and arguments." />
          <attvalue for="2" value="2" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="8.1.2 &#23454;&#21442;&#21644;&#24418;&#21442;" label="8.1.2 &#23454;&#21442;&#21644;&#24418;&#21442;">
        <attvalues>
          <attvalue for="0" value="EVENT" />
          <attvalue for="1" value="Section 8.1.2, &quot;Arguments and Parameters,&quot; is a subsection that explains the difference between parameters (in function definitions) and arguments (in function calls) in Python." />
          <attvalue for="2" value="2" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="&#20989;&#25968;" label="&#20989;&#25968;">
        <attvalues>
          <attvalue for="0" value="EVENT" />
          <attvalue for="1" value="A function is a named block of code in Python designed to perform a specific task, which can be defined, called, and reused throughout a program." />
          <attvalue for="2" value="4" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="&#20989;&#25968;&#23450;&#20041;" label="&#20989;&#25968;&#23450;&#20041;">
        <attvalues>
          <attvalue for="0" value="EVENT" />
          <attvalue for="1" value="Function definition is the process of creating a new function in Python using the def keyword, specifying its name, parameters, and body." />
          <attvalue for="2" value="3" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="&#20989;&#25968;&#35843;&#29992;" label="&#20989;&#25968;&#35843;&#29992;">
        <attvalues>
          <attvalue for="0" value="EVENT" />
          <attvalue for="1" value="Function call is the act of executing a function in Python by using its name and providing any required arguments." />
          <attvalue for="2" value="1" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="&#25991;&#26723;&#23383;&#31526;&#20018;" label="&#25991;&#26723;&#23383;&#31526;&#20018;">
        <attvalues>
          <attvalue for="0" value="EVENT" />
          <attvalue for="1" value="A docstring is a special string literal in Python that appears right after the function definition and is used to document the function&#8217;s purpose and behavior." />
          <attvalue for="2" value="1" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="PRINT" label="PRINT">
        <attvalues>
          <attvalue for="0" value="EVENT" />
          <attvalue for="1" value="print is a built-in Python function used to output text or other information to the console." />
          <attvalue for="2" value="1" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="DEF" label="DEF">
        <attvalues>
          <attvalue for="0" value="EVENT" />
          <attvalue for="1" value="def is a Python keyword used to define a new function." />
          <attvalue for="2" value="1" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="&#20027;&#31243;&#24207;&#25991;&#20214;" label="&#20027;&#31243;&#24207;&#25991;&#20214;">
        <attvalues>
          <attvalue for="0" value="ORGANIZATION" />
          <attvalue for="1" value="The main program file is the primary Python script that serves as the entry point for running the program, which can import and use functions from modules." />
          <attvalue for="2" value="1" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="&#31532; 8 &#31456;" label="&#31532; 8 &#31456;">
        <attvalues>
          <attvalue for="0" value="" />
          <attvalue for="1" value="" />
          <attvalue for="2" value="3" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="&#21442;&#25968;" label="&#21442;&#25968;">
        <attvalues>
          <attvalue for="0" value="" />
          <attvalue for="1" value="" />
          <attvalue for="2" value="2" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="ALICE IN WONDERLAND" label="ALICE IN WONDERLAND">
        <attvalues>
          <attvalue for="0" value="EVENT" />
          <attvalue for="1" value="Alice in Wonderland is the title of a book used as an example in the favorite_book() function, illustrating how to pass a book title as an argument to a function." />
          <attvalue for="2" value="1" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="HARRY" label="HARRY">
        <attvalues>
          <attvalue for="0" value="PERSON" />
          <attvalue for="1" value="Harry is the name of a hamster that serves as an example pet in the describe_pet() function, which is commonly used to demonstrate how to pass pet information as arguments to a function in Python. In the example code, Harry is specifically mentioned as a pet hamster, illustrating the process of providing arguments to a function that describes pets. Through the use of Harry, learners can better understand how to structure and utilize functions in Python programming by passing relevant details, such as the pet's name and type, as arguments." />
          <attvalue for="2" value="4" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="WILLIE" label="WILLIE">
        <attvalues>
          <attvalue for="0" value="PERSON" />
          <attvalue for="1" value="Willie is the name of a dog used as an example pet in the describe_pet() function, which is commonly featured in Python programming tutorials. In these examples, Willie serves as a sample pet to demonstrate how to pass different pet names and types as arguments to a function. Specifically, Willie is referenced as a pet dog in the example code, illustrating the process of providing arguments to a function designed to describe pets. This use of Willie helps learners understand how to work with functions and arguments in Python by providing a clear, relatable example." />
          <attvalue for="2" value="4" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="FAVORITE_BOOK" label="FAVORITE_BOOK">
        <attvalues>
          <attvalue for="0" value="EVENT" />
          <attvalue for="1" value="Favorite_book is the name of a function defined in the text as an exercise, which takes a parameter 'title' and prints a message about a favorite book, used to demonstrate passing arguments to functions in Python." />
          <attvalue for="2" value="2" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="DESCRIBE_PET" label="DESCRIBE_PET">
        <attvalues>
          <attvalue for="0" value="EVENT" />
          <attvalue for="1" value="The &quot;describe_pet&quot; (also referred to as &quot;Describe_pet&quot;) is a Python function defined in the provided text to illustrate the use of function parameters and arguments. This function takes two parameters: animal_type and pet_name. When called, it prints or displays information about a pet, utilizing the values provided for animal_type and pet_name. The function is used in examples to demonstrate how to define a function with parameters and how to call it with specific arguments in Python programming. Overall, &quot;describe_pet&quot; serves as an educational tool to help users understand the basics of function definition and invocation in Python, specifically in the context of handling and displaying information about pets." />
          <attvalue for="2" value="5" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="PETS.PY" label="PETS.PY">
        <attvalues>
          <attvalue for="0" value="EVENT" />
          <attvalue for="1" value="PETS.PY is a Python script file referenced in instructional materials as an example for learning about function arguments and parameters. The script contains the definition and usage of the describe_pet() function, which is used to demonstrate how function calls and argument passing work in Python. PETS.PY serves as a practical example for learners to understand the concepts of defining functions, specifying parameters, and providing arguments when calling functions. The file is commonly used in educational contexts to illustrate these fundamental programming concepts." />
          <attvalue for="2" value="4" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="CHAPTER" label="CHAPTER">
        <attvalues>
          <attvalue for="0" value="EVENT" />
          <attvalue for="1" value="Chapter refers to the current section or topic of the instructional text, which is about functions, parameters, and arguments in Python programming." />
          <attvalue for="2" value="3" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="DISPLAY_MESSAGE" label="DISPLAY_MESSAGE">
        <attvalues>
          <attvalue for="0" value="" />
          <attvalue for="1" value="" />
          <attvalue for="2" value="2" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="DOG" label="DOG">
        <attvalues>
          <attvalue for="0" value="PERSON" />
          <attvalue for="1" value="Dog is an animal type used as an example in the Python code, specifically as the species of the pet named Willie." />
          <attvalue for="2" value="1" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="ANIMAL_TYPE" label="ANIMAL_TYPE">
        <attvalues>
          <attvalue for="0" value="EVENT" />
          <attvalue for="1" value="animal_type is a function parameter in the Python examples, representing the type of animal being described (e.g., 'dog', 'hamster')." />
          <attvalue for="2" value="2" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="PET_NAME" label="PET_NAME">
        <attvalues>
          <attvalue for="0" value="EVENT" />
          <attvalue for="1" value="pet_name is a function parameter in the Python examples, representing the name of the pet being described." />
          <attvalue for="2" value="2" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="HAMSTER" label="HAMSTER">
        <attvalues>
          <attvalue for="0" value="" />
          <attvalue for="1" value="" />
          <attvalue for="2" value="1" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="JIMI HENDRIX" label="JIMI HENDRIX">
        <attvalues>
          <attvalue for="0" value="PERSON" />
          <attvalue for="1" value="Jimi Hendrix is a person whose name is used as an example in the text to demonstrate how to format and print names using Python functions. He is referenced as a musician and his name is used in code examples for output formatting." />
          <attvalue for="2" value="1" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="JOHN LEE HOOKER" label="JOHN LEE HOOKER">
        <attvalues>
          <attvalue for="0" value="PERSON" />
          <attvalue for="1" value="John Lee Hooker is a person whose name is used as an example in the text to demonstrate how to format and print names using Python functions. His full name, including a middle name, is used in code examples for output formatting." />
          <attvalue for="2" value="2" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="JOHN HOOKER" label="JOHN HOOKER">
        <attvalues>
          <attvalue for="0" value="PERSON" />
          <attvalue for="1" value="John Hooker is a person whose name is used as an example in the text to demonstrate how to format and print names using Python functions, specifically in the context of handling optional middle names." />
          <attvalue for="2" value="1" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="GET_FORMATTED_NAME" label="GET_FORMATTED_NAME">
        <attvalues>
          <attvalue for="0" value="EVENT" />
          <attvalue for="1" value="get_formatted_name is a Python function described in the text, which formats a person's name by combining first, (optional) middle, and last names into a standard format. It is used as an example to teach function arguments and default values." />
          <attvalue for="2" value="10" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="LEE" label="LEE">
        <attvalues>
          <attvalue for="0" value="PERSON" />
          <attvalue for="1" value="Lee is used as a middle name in the example 'John Lee Hooker' to demonstrate how the get_formatted_name function handles optional middle names." />
          <attvalue for="2" value="1" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="FIRST_NAME" label="FIRST_NAME">
        <attvalues>
          <attvalue for="0" value="PERSON" />
          <attvalue for="1" value="First_name is a parameter in the get_formatted_name function, representing the given name of a person in the code examples." />
          <attvalue for="2" value="1" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="MIDDLE_NAME" label="MIDDLE_NAME">
        <attvalues>
          <attvalue for="0" value="PERSON" />
          <attvalue for="1" value="Middle_name is a parameter in the get_formatted_name function, representing the optional middle name of a person in the code examples." />
          <attvalue for="2" value="1" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="LAST_NAME" label="LAST_NAME">
        <attvalues>
          <attvalue for="0" value="PERSON" />
          <attvalue for="1" value="Last_name is a parameter in the get_formatted_name function, representing the family name of a person in the code examples." />
          <attvalue for="2" value="1" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="STANDARD FORMAT" label="STANDARD FORMAT">
        <attvalues>
          <attvalue for="0" value="EVENT" />
          <attvalue for="1" value="Standard format refers to the output format of names as demonstrated in the code examples, where names are capitalized and properly spaced." />
          <attvalue for="2" value="1" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="CODE EXAMPLE" label="CODE EXAMPLE">
        <attvalues>
          <attvalue for="0" value="EVENT" />
          <attvalue for="1" value="Code example refers to the Python code snippets provided in the text to illustrate how to use the get_formatted_name function and print formatted names." />
          <attvalue for="2" value="2" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="MUSICIAN" label="MUSICIAN">
        <attvalues>
          <attvalue for="0" value="" />
          <attvalue for="1" value="" />
          <attvalue for="2" value="1" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="MAKE_PIZZA" label="MAKE_PIZZA">
        <attvalues>
          <attvalue for="0" value="ORGANIZATION" />
          <attvalue for="1" value="make_pizza is a function in the pizza module, used to create pizzas with specified sizes and toppings. It is frequently used as an example in Python programming to demonstrate function import and aliasing techniques." />
          <attvalue for="2" value="8" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="PIZZA" label="PIZZA">
        <attvalues>
          <attvalue for="0" value="ORGANIZATION" />
          <attvalue for="1" value="pizza is a Python module that contains the make_pizza function and possibly other related functions. It is used as an example module for demonstrating import statements, aliasing, and function usage in Python." />
          <attvalue for="2" value="3" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="MP" label="MP">
        <attvalues>
          <attvalue for="0" value="ORGANIZATION" />
          <attvalue for="1" value="mp is an alias for the make_pizza function, created using the 'as' keyword in Python import statements to simplify function calls and avoid naming conflicts." />
          <attvalue for="2" value="2" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="P" label="P">
        <attvalues>
          <attvalue for="0" value="ORGANIZATION" />
          <attvalue for="1" value="p is an alias for the pizza module, created using the 'as' keyword in Python import statements to make module function calls more concise." />
          <attvalue for="2" value="2" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="MODULE_NAME" label="MODULE_NAME">
        <attvalues>
          <attvalue for="0" value="ORGANIZATION" />
          <attvalue for="1" value="module_name is a placeholder representing any Python module, used in example import statements to illustrate syntax for importing functions or modules with aliases or wildcards." />
          <attvalue for="2" value="3" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="FUNCTION_NAME" label="FUNCTION_NAME">
        <attvalues>
          <attvalue for="0" value="ORGANIZATION" />
          <attvalue for="1" value="function_name is a placeholder representing any function within a Python module, used in example import statements to illustrate syntax for importing functions with aliases." />
          <attvalue for="2" value="1" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="FN" label="FN">
        <attvalues>
          <attvalue for="0" value="ORGANIZATION" />
          <attvalue for="1" value="fn is an alias for a function, used as a placeholder in example import statements to demonstrate how to rename functions upon import." />
          <attvalue for="2" value="1" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="MN" label="MN">
        <attvalues>
          <attvalue for="0" value="ORGANIZATION" />
          <attvalue for="1" value="mn is an alias for a module, used as a placeholder in example import statements to demonstrate how to rename modules upon import." />
          <attvalue for="2" value="1" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="IMPORTING FUNCTIONS WITH ALIAS" label="IMPORTING FUNCTIONS WITH ALIAS">
        <attvalues>
          <attvalue for="0" value="" />
          <attvalue for="1" value="" />
          <attvalue for="2" value="2" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="IMPORTING MODULES WITH ALIAS" label="IMPORTING MODULES WITH ALIAS">
        <attvalues>
          <attvalue for="0" value="" />
          <attvalue for="1" value="" />
          <attvalue for="2" value="2" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="WILDCARD IMPORT" label="WILDCARD IMPORT">
        <attvalues>
          <attvalue for="0" value="" />
          <attvalue for="1" value="" />
          <attvalue for="2" value="2" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="FUNCTION NAMING GUIDELINES" label="FUNCTION NAMING GUIDELINES">
        <attvalues>
          <attvalue for="0" value="" />
          <attvalue for="1" value="" />
          <attvalue for="2" value="1" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="MUSHROOMS" label="MUSHROOMS">
        <attvalues>
          <attvalue for="0" value="EVENT" />
          <attvalue for="1" value="Mushrooms are used as an example pizza topping in the make_pizza function calls, representing a possible event or action within the context of pizza creation." />
          <attvalue for="2" value="1" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="GREEN PEPPERS" label="GREEN PEPPERS">
        <attvalues>
          <attvalue for="0" value="EVENT" />
          <attvalue for="1" value="Green peppers are used as an example pizza topping in the make_pizza function calls, representing a possible event or action within the context of pizza creation." />
          <attvalue for="2" value="1" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="EXTRA CHEESE" label="EXTRA CHEESE">
        <attvalues>
          <attvalue for="0" value="EVENT" />
          <attvalue for="1" value="Extra cheese is used as an example pizza topping in the make_pizza function calls, representing a possible event or action within the context of pizza creation." />
          <attvalue for="2" value="1" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="PEPPERONI" label="PEPPERONI">
        <attvalues>
          <attvalue for="0" value="" />
          <attvalue for="1" value="" />
          <attvalue for="2" value="1" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
      <node id="&#31532; 9 &#31456;" label="&#31532; 9 &#31456;">
        <attvalues>
          <attvalue for="0" value="EVENT" />
          <attvalue for="1" value="Chapter 9 is a section in a book or instructional material where the reader will learn about writing classes, which encapsulate functions and data for flexible and efficient use." />
          <attvalue for="2" value="0" />
          <attvalue for="3" value="[]" />
        </attvalues>
      </node>
    </nodes>
    <edges>
      <edge source="PYTHON" target="GREETER.PY" id="0" weight="8.0">
        <attvalues>
          <attvalue for="4" value="greeter.py is a Python script, written and executed in the Python programming language" />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="PYTHON" target="GREET_USER" id="1" weight="8.0">
        <attvalues>
          <attvalue for="4" value="greet_user is a function written in Python and executed by the Python interpreter" />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="PYTHON" target="MODULE" id="2" weight="8.0">
        <attvalues>
          <attvalue for="4" value="A module is a Python file that can be imported into other Python programs" />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="PYTHON" target="JESSE" id="3" weight="7.0">
        <attvalues>
          <attvalue for="4" value="Jesse is used as an example argument in a Python function to demonstrate how to pass information to a function parameter." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="PYTHON" target="ALICE IN WONDERLAND" id="4" weight="6.0">
        <attvalues>
          <attvalue for="4" value="Alice in Wonderland is used as an example book title in a Python function to illustrate passing arguments." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="PYTHON" target="HARRY" id="5" weight="13.0">
        <attvalues>
          <attvalue for="4" value="Harry is an example pet name commonly used in Python code to demonstrate how function argument passing works. In Python programming tutorials and examples, &quot;Harry&quot; is often chosen as the name of a pet within functions to illustrate the concept of passing arguments to functions. This helps learners understand how data can be provided to functions and how those functions can operate on the provided arguments. The use of &quot;Harry&quot; as a sample pet name is a teaching tool within the context of Python, making abstract programming concepts more relatable and easier to grasp." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="PYTHON" target="WILLIE" id="6" weight="13.0">
        <attvalues>
          <attvalue for="4" value="Willie is commonly used as an example pet name in Python programming to demonstrate how function argument passing works. In Python code examples, Willie often appears as a sample argument in functions related to pets, helping illustrate the concept of passing values to functions. This usage of Willie serves as a clear and relatable way for learners to understand how arguments are handled in Python functions." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="PYTHON" target="DISPLAY_MESSAGE" id="7" weight="7.0">
        <attvalues>
          <attvalue for="4" value="Display_message is a function written in Python to demonstrate function definition and invocation." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="PYTHON" target="FAVORITE_BOOK" id="8" weight="7.0">
        <attvalues>
          <attvalue for="4" value="Favorite_book is a function written in Python to demonstrate passing arguments to functions." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="PYTHON" target="DESCRIBE_PET" id="9" weight="7.0">
        <attvalues>
          <attvalue for="4" value="Describe_pet is a function written in Python to demonstrate the use of multiple parameters and arguments." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="PYTHON" target="PETS.PY" id="10" weight="16.0">
        <attvalues>
          <attvalue for="4" value="PYTHON is a widely used programming language known for its readability and versatility. PETS.PY is a Python script file that contains examples of functions, specifically designed to demonstrate how function argument passing and default values work in Python. This script serves as an educational resource, illustrating key concepts related to defining and calling functions, handling arguments, and utilizing default parameter values within the Python programming language." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="PYTHON" target="ANIMAL_TYPE" id="11" weight="7.0">
        <attvalues>
          <attvalue for="4" value="animal_type is a parameter used in Python function examples." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="PYTHON" target="PET_NAME" id="12" weight="1.0">
        <attvalues>
          <attvalue for="4" value="pet_name is a parameter used in Python function examples." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="PYTHON" target="GET_FORMATTED_NAME" id="13" weight="1.0">
        <attvalues>
          <attvalue for="4" value="get_formatted_name is a function written in the Python programming language, used in the text to illustrate Python's handling of function arguments and default values." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="PYTHON" target="CODE EXAMPLE" id="14" weight="1.0">
        <attvalues>
          <attvalue for="4" value="The code examples are written in the Python programming language." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="PYTHON" target="MAKE_PIZZA" id="15" weight="7.0">
        <attvalues>
          <attvalue for="4" value="make_pizza is a function used in Python programming examples." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="PYTHON" target="PIZZA" id="16" weight="7.0">
        <attvalues>
          <attvalue for="4" value="pizza is a module used in Python programming to demonstrate import and aliasing techniques." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="PYTHON" target="IMPORTING FUNCTIONS WITH ALIAS" id="17" weight="8.0">
        <attvalues>
          <attvalue for="4" value="Importing functions with alias is a feature of Python's import system." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="PYTHON" target="IMPORTING MODULES WITH ALIAS" id="18" weight="8.0">
        <attvalues>
          <attvalue for="4" value="Importing modules with alias is a feature of Python's import system." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="PYTHON" target="WILDCARD IMPORT" id="19" weight="8.0">
        <attvalues>
          <attvalue for="4" value="Wildcard import is a feature of Python's import system." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="PYTHON" target="FUNCTION NAMING GUIDELINES" id="20" weight="7.0">
        <attvalues>
          <attvalue for="4" value="Function naming guidelines are part of Python's best practices for code clarity." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="GREETER.PY" target="GREET_USER" id="21" weight="9.0">
        <attvalues>
          <attvalue for="4" value="The greeter.py file contains the definition and implementation of the greet_user function" />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="GREETER.PY" target="MODULE" id="22" weight="1.0">
        <attvalues>
          <attvalue for="4" value="greeter.py is an example of a Python module containing function definitions" />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="GREET_USER" target="USERNAME" id="23" weight="9.0">
        <attvalues>
          <attvalue for="4" value="The greet_user function takes username as a parameter to personalize the greeting" />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="GREET_USER" target="JESSE" id="24" weight="7.0">
        <attvalues>
          <attvalue for="4" value="Jesse is an example value passed as the username parameter to the greet_user function" />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="GREET_USER" target="SARAH" id="25" weight="7.0">
        <attvalues>
          <attvalue for="4" value="Sarah is an example value passed as the username parameter to the greet_user function" />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="MODULE" target="&#20027;&#31243;&#24207;&#25991;&#20214;" id="26" weight="1.0">
        <attvalues>
          <attvalue for="4" value="The main program file can import and use functions from modules" />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="8.1 &#23450;&#20041;&#20989;&#25968;" target="&#31532; 8 &#31456;" id="27" weight="9.0">
        <attvalues>
          <attvalue for="4" value="Section 8.1 is a subsection of Chapter 8, focusing on defining functions" />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="8.1 &#23450;&#20041;&#20989;&#25968;" target="&#20989;&#25968;&#23450;&#20041;" id="28" weight="9.0">
        <attvalues>
          <attvalue for="4" value="Section 8.1 explains the concept and process of function definition in Python" />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="8.1.1 &#21521;&#20989;&#25968;&#20256;&#36882;&#20449;&#24687;" target="&#31532; 8 &#31456;" id="29" weight="9.0">
        <attvalues>
          <attvalue for="4" value="Section 8.1.1 is a subsection of Chapter 8, focusing on passing information to functions" />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="8.1.1 &#21521;&#20989;&#25968;&#20256;&#36882;&#20449;&#24687;" target="&#21442;&#25968;" id="30" weight="8.0">
        <attvalues>
          <attvalue for="4" value="Section 8.1.1 discusses how to pass parameters to functions" />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="8.1.2 &#23454;&#21442;&#21644;&#24418;&#21442;" target="&#31532; 8 &#31456;" id="31" weight="9.0">
        <attvalues>
          <attvalue for="4" value="Section 8.1.2 is a subsection of Chapter 8, focusing on arguments and parameters" />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="8.1.2 &#23454;&#21442;&#21644;&#24418;&#21442;" target="&#21442;&#25968;" id="32" weight="8.0">
        <attvalues>
          <attvalue for="4" value="Section 8.1.2 explains the difference between arguments and parameters" />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="&#20989;&#25968;" target="&#20989;&#25968;&#23450;&#20041;" id="33" weight="9.0">
        <attvalues>
          <attvalue for="4" value="A function is created through function definition" />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="&#20989;&#25968;" target="&#20989;&#25968;&#35843;&#29992;" id="34" weight="9.0">
        <attvalues>
          <attvalue for="4" value="A function is executed through a function call" />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="&#20989;&#25968;" target="&#25991;&#26723;&#23383;&#31526;&#20018;" id="35" weight="8.0">
        <attvalues>
          <attvalue for="4" value="A function can have a docstring to describe its purpose" />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="&#20989;&#25968;" target="PRINT" id="36" weight="7.0">
        <attvalues>
          <attvalue for="4" value="The print function is often used within user-defined functions to display output" />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="&#20989;&#25968;&#23450;&#20041;" target="DEF" id="37" weight="9.0">
        <attvalues>
          <attvalue for="4" value="The def keyword is used in Python to define a function" />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="HARRY" target="WILLIE" id="38" weight="1.0">
        <attvalues>
          <attvalue for="4" value="Harry and Willie are both example pet names used in the describe_pet() function to show multiple function calls with different arguments." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="HARRY" target="PETS.PY" id="39" weight="1.0">
        <attvalues>
          <attvalue for="4" value="Harry is a pet described in the pets.py example." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="HARRY" target="HAMSTER" id="40" weight="10.0">
        <attvalues>
          <attvalue for="4" value="Harry is a hamster, as shown in the example code." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="WILLIE" target="PETS.PY" id="41" weight="6.0">
        <attvalues>
          <attvalue for="4" value="Willie is a pet described in the pets.py example." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="WILLIE" target="DOG" id="42" weight="10.0">
        <attvalues>
          <attvalue for="4" value="Willie is a dog, as shown in the example code." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="FAVORITE_BOOK" target="CHAPTER" id="43" weight="6.0">
        <attvalues>
          <attvalue for="4" value="Favorite_book is an exercise function meant to illustrate the topic of the chapter." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="DESCRIBE_PET" target="PETS.PY" id="44" weight="8.0">
        <attvalues>
          <attvalue for="4" value="The describe_pet function is defined and called in the pets.py script." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="DESCRIBE_PET" target="CHAPTER" id="45" weight="1.0">
        <attvalues>
          <attvalue for="4" value="Describe_pet is an exercise function meant to illustrate the topic of the chapter." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="DESCRIBE_PET" target="ANIMAL_TYPE" id="46" weight="9.0">
        <attvalues>
          <attvalue for="4" value="The describe_pet function takes animal_type as a parameter to specify the species of the pet." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="DESCRIBE_PET" target="PET_NAME" id="47" weight="9.0">
        <attvalues>
          <attvalue for="4" value="The describe_pet function takes pet_name as a parameter to specify the name of the pet." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="CHAPTER" target="DISPLAY_MESSAGE" id="48" weight="6.0">
        <attvalues>
          <attvalue for="4" value="Display_message is an exercise function meant to illustrate the topic of the chapter." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="JIMI HENDRIX" target="GET_FORMATTED_NAME" id="49" weight="8.0">
        <attvalues>
          <attvalue for="4" value="Jimi Hendrix's name is used as an example input and output for the get_formatted_name function." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="JOHN LEE HOOKER" target="GET_FORMATTED_NAME" id="50" weight="8.0">
        <attvalues>
          <attvalue for="4" value="John Lee Hooker's name is used as an example input and output for the get_formatted_name function, demonstrating the use of a middle name." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="JOHN LEE HOOKER" target="LEE" id="51" weight="8.0">
        <attvalues>
          <attvalue for="4" value="Lee is the middle name in the full name John Lee Hooker, as used in the code example." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="JOHN HOOKER" target="GET_FORMATTED_NAME" id="52" weight="8.0">
        <attvalues>
          <attvalue for="4" value="John Hooker's name is used as an example input and output for the get_formatted_name function, demonstrating the case without a middle name." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="GET_FORMATTED_NAME" target="MUSICIAN" id="53" weight="9.0">
        <attvalues>
          <attvalue for="4" value="The variable musician is assigned the value returned by the get_formatted_name function in the code examples." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="GET_FORMATTED_NAME" target="FIRST_NAME" id="54" weight="9.0">
        <attvalues>
          <attvalue for="4" value="First_name is a parameter of the get_formatted_name function, representing the first part of a person's name." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="GET_FORMATTED_NAME" target="MIDDLE_NAME" id="55" weight="9.0">
        <attvalues>
          <attvalue for="4" value="Middle_name is a parameter of the get_formatted_name function, representing the optional middle part of a person's name." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="GET_FORMATTED_NAME" target="LAST_NAME" id="56" weight="9.0">
        <attvalues>
          <attvalue for="4" value="Last_name is a parameter of the get_formatted_name function, representing the last part of a person's name." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="GET_FORMATTED_NAME" target="STANDARD FORMAT" id="57" weight="8.0">
        <attvalues>
          <attvalue for="4" value="The get_formatted_name function returns names in a standard format, as described in the text." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="GET_FORMATTED_NAME" target="CODE EXAMPLE" id="58" weight="8.0">
        <attvalues>
          <attvalue for="4" value="The code examples in the text demonstrate the use of the get_formatted_name function." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="MAKE_PIZZA" target="PIZZA" id="59" weight="10.0">
        <attvalues>
          <attvalue for="4" value="make_pizza is a function defined within the pizza module." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="MAKE_PIZZA" target="MP" id="60" weight="9.0">
        <attvalues>
          <attvalue for="4" value="mp is an alias for the make_pizza function, created to simplify function calls." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="MAKE_PIZZA" target="WILDCARD IMPORT" id="61" weight="1.0">
        <attvalues>
          <attvalue for="4" value="Wildcard import allows direct use of make_pizza without module or alias prefix." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="MAKE_PIZZA" target="PEPPERONI" id="62" weight="6.0">
        <attvalues>
          <attvalue for="4" value="make_pizza is called with pepperoni as a topping in the example." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="MAKE_PIZZA" target="MUSHROOMS" id="63" weight="6.0">
        <attvalues>
          <attvalue for="4" value="make_pizza is called with mushrooms as a topping in the example." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="MAKE_PIZZA" target="GREEN PEPPERS" id="64" weight="6.0">
        <attvalues>
          <attvalue for="4" value="make_pizza is called with green peppers as a topping in the example." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="MAKE_PIZZA" target="EXTRA CHEESE" id="65" weight="1.0">
        <attvalues>
          <attvalue for="4" value="make_pizza is called with extra cheese as a topping in the example." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="PIZZA" target="P" id="66" weight="9.0">
        <attvalues>
          <attvalue for="4" value="p is an alias for the pizza module, created to make function calls more concise." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="MP" target="IMPORTING FUNCTIONS WITH ALIAS" id="67" weight="8.0">
        <attvalues>
          <attvalue for="4" value="mp is an example of importing a function with an alias." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="P" target="IMPORTING MODULES WITH ALIAS" id="68" weight="8.0">
        <attvalues>
          <attvalue for="4" value="p is an example of importing a module with an alias." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="MODULE_NAME" target="FUNCTION_NAME" id="69" weight="6.0">
        <attvalues>
          <attvalue for="4" value="function_name is a function within module_name, used as a placeholder in import examples." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="MODULE_NAME" target="FN" id="70" weight="6.0">
        <attvalues>
          <attvalue for="4" value="fn is an alias for function_name in module_name, used in import examples." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
      <edge source="MODULE_NAME" target="MN" id="71" weight="6.0">
        <attvalues>
          <attvalue for="4" value="mn is an alias for module_name, used in import examples." />
          <attvalue for="5" value="0" />
        </attvalues>
      </edge>
    </edges>
  </graph>
</gexf>