<?xml version="1.0" ?>
<gexf xmlns="http://www.gexf.net/1.2draft" version="1.2">
  <meta lastmodifieddate="2025-06-27">
    <creator>Python Knowledge Graph Generator</creator>
  </meta>
  <graph mode="static" defaultedgetype="undirected">
    <attributes class="node">
      <attribute id="0" title="type" type="string"/>
      <attribute id="1" title="size" type="integer"/>
      <attribute id="2" title="color" type="string"/>
    </attributes>
    <attributes class="edge">
      <attribute id="0" title="relationship" type="string"/>
      <attribute id="1" title="weight" type="float"/>
    </attributes>
    <nodes>
      <node id="node_0" label="函数">
        <attvalues>
          <attvalue for="0" value="core"/>
          <attvalue for="1" value="60"/>
          <attvalue for="2" value="red"/>
        </attvalues>
      </node>
      <node id="node_1" label="实参">
        <attvalues>
          <attvalue for="0" value="param"/>
          <attvalue for="1" value="45"/>
          <attvalue for="2" value="cyan"/>
        </attvalues>
      </node>
      <node id="node_2" label="形参">
        <attvalues>
          <attvalue for="0" value="param"/>
          <attvalue for="1" value="45"/>
          <attvalue for="2" value="cyan"/>
        </attvalues>
      </node>
      <node id="node_3" label="位置实参">
        <attvalues>
          <attvalue for="0" value="param_type"/>
          <attvalue for="1" value="35"/>
          <attvalue for="2" value="blue"/>
        </attvalues>
      </node>
      <node id="node_4" label="位置实参的顺序">
        <attvalues>
          <attvalue for="0" value="rule"/>
          <attvalue for="1" value="25"/>
          <attvalue for="2" value="pink"/>
        </attvalues>
      </node>
      <node id="node_5" label="关键字实参">
        <attvalues>
          <attvalue for="0" value="param_type"/>
          <attvalue for="1" value="35"/>
          <attvalue for="2" value="blue"/>
        </attvalues>
      </node>
      <node id="node_6" label="默认值">
        <attvalues>
          <attvalue for="0" value="feature"/>
          <attvalue for="1" value="30"/>
          <attvalue for="2" value="purple"/>
        </attvalues>
      </node>
      <node id="node_7" label="返回值">
        <attvalues>
          <attvalue for="0" value="output"/>
          <attvalue for="1" value="35"/>
          <attvalue for="2" value="orange"/>
        </attvalues>
      </node>
      <node id="node_8" label="while循环">
        <attvalues>
          <attvalue for="0" value="control"/>
          <attvalue for="1" value="40"/>
          <attvalue for="2" value="green"/>
        </attvalues>
      </node>
      <node id="node_9" label="列表">
        <attvalues>
          <attvalue for="0" value="data"/>
          <attvalue for="1" value="40"/>
          <attvalue for="2" value="yellow"/>
        </attvalues>
      </node>
    </nodes>
    <edges>
      <edge id="0" source="node_0" target="node_1">
        <attvalues>
          <attvalue for="0" value="contains"/>
          <attvalue for="1" value="3.0"/>
        </attvalues>
      </edge>
      <edge id="1" source="node_0" target="node_2">
        <attvalues>
          <attvalue for="0" value="contains"/>
          <attvalue for="1" value="3.0"/>
        </attvalues>
      </edge>
      <edge id="2" source="node_0" target="node_7">
        <attvalues>
          <attvalue for="0" value="contains"/>
          <attvalue for="1" value="3.0"/>
        </attvalues>
      </edge>
      <edge id="3" source="node_1" target="node_3">
        <attvalues>
          <attvalue for="0" value="contains"/>
          <attvalue for="1" value="2.0"/>
        </attvalues>
      </edge>
      <edge id="4" source="node_1" target="node_5">
        <attvalues>
          <attvalue for="0" value="contains"/>
          <attvalue for="1" value="2.0"/>
        </attvalues>
      </edge>
      <edge id="5" source="node_3" target="node_4">
        <attvalues>
          <attvalue for="0" value="contains"/>
          <attvalue for="1" value="2.0"/>
        </attvalues>
      </edge>
      <edge id="6" source="node_2" target="node_6">
        <attvalues>
          <attvalue for="0" value="contains"/>
          <attvalue for="1" value="2.0"/>
        </attvalues>
      </edge>
      <edge id="7" source="node_1" target="node_2">
        <attvalues>
          <attvalue for="0" value="corresponds"/>
          <attvalue for="1" value="2.0"/>
        </attvalues>
      </edge>
      <edge id="8" source="node_3" target="node_5">
        <attvalues>
          <attvalue for="0" value="alternative"/>
          <attvalue for="1" value="1.5"/>
        </attvalues>
      </edge>
      <edge id="9" source="node_0" target="node_8">
        <attvalues>
          <attvalue for="0" value="uses"/>
          <attvalue for="1" value="1.0"/>
        </attvalues>
      </edge>
      <edge id="10" source="node_8" target="node_9">
        <attvalues>
          <attvalue for="0" value="processes"/>
          <attvalue for="1" value="1.0"/>
        </attvalues>
      </edge>
    </edges>
  </graph>
</gexf>