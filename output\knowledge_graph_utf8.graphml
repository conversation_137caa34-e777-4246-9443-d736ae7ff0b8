<?xml version='1.0' encoding='utf-8'?>
<graphml xmlns="http://graphml.graphdrawing.org/xmlns" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://graphml.graphdrawing.org/xmlns http://graphml.graphdrawing.org/xmlns/1.0/graphml.xsd">
  <key id="d7" for="edge" attr.name="rank" attr.type="long" />
  <key id="d6" for="edge" attr.name="description" attr.type="string" />
  <key id="d5" for="edge" attr.name="weight" attr.type="double" />
  <key id="d4" for="node" attr.name="community_ids" attr.type="string" />
  <key id="d3" for="node" attr.name="degree" attr.type="long" />
  <key id="d2" for="node" attr.name="description" attr.type="string" />
  <key id="d1" for="node" attr.name="type" attr.type="string" />
  <key id="d0" for="node" attr.name="label" attr.type="string" />
  <graph edgedefault="undirected">
    <node id="PYTHON">
      <data key="d0">PYTHON</data>
      <data key="d1">ORGANIZATION</data>
      <data key="d2">Python is a high-level programming language widely used for general-purpose programming, known for its readability and ease of use. In the context provided, Python serves as the language in which example functions and modules are written and executed, particularly for demonstrating concepts such as function argument passing and code for formatting names. Python supports a variety of features, including modules, functions, and multiple import mechanisms such as aliasing and wildcard imports, making it a versatile tool for both teaching and practical application. Throughout the text, Python is referenced as the primary language for illustrating programming concepts and executing example code.</data>
      <data key="d3">21</data>
      <data key="d4">[]</data>
    </node>
    <node id="GREETER.PY">
      <data key="d0">GREETER.PY</data>
      <data key="d1">ORGANIZATION</data>
      <data key="d2">greeter.py is a Python script file that contains the definition and implementation of the greet_user() function, serving as an example for function creation and usage.</data>
      <data key="d3">3</data>
      <data key="d4">[]</data>
    </node>
    <node id="GREET_USER">
      <data key="d0">GREET_USER</data>
      <data key="d1">EVENT</data>
      <data key="d2">greet_user is a function defined in Python that prints a greeting message. It can be called with or without a username to display a personalized or generic greeting.</data>
      <data key="d3">5</data>
      <data key="d4">[]</data>
    </node>
    <node id="USERNAME">
      <data key="d0">USERNAME</data>
      <data key="d1">PERSON</data>
      <data key="d2">username is a parameter in the greet_user function, representing the name of the user to be greeted. It is a placeholder for any person's name passed to the function.</data>
      <data key="d3">1</data>
      <data key="d4">[]</data>
    </node>
    <node id="MODULE">
      <data key="d0">MODULE</data>
      <data key="d1">ORGANIZATION</data>
      <data key="d2">A module in Python is an independent file containing functions, classes, or variables, which can be imported into the main program to keep the code organized and maintainable.</data>
      <data key="d3">3</data>
      <data key="d4">[]</data>
    </node>
    <node id="JESSE">
      <data key="d0">JESSE</data>
      <data key="d1">PERSON</data>
      <data key="d2">JESSE is a person whose name is commonly used as an example argument in Python programming, particularly in demonstrations of how to pass values to function parameters. In instructional materials, JESSE often appears as an example username in the function call greet_user('jesse'), where the function is designed to provide a personalized greeting to the user. This usage helps illustrate how functions can accept input and generate customized output based on the provided argument. JESSE, therefore, serves as a representative user in programming examples, making it easier for learners to understand the concept of passing arguments to functions and creating personalized interactions in code.</data>
      <data key="d3">2</data>
      <data key="d4">[]</data>
    </node>
    <node id="SARAH">
      <data key="d0">SARAH</data>
      <data key="d1">PERSON</data>
      <data key="d2">Sarah is another example username used in the greet_user('sarah') function call, representing a user who receives a personalized greeting.</data>
      <data key="d3">1</data>
      <data key="d4">[]</data>
    </node>
    <node id="8.1 定义函数">
      <data key="d0">8.1 定义函数</data>
      <data key="d1">EVENT</data>
      <data key="d2">Section 8.1, titled "Defining Functions," is a subsection of Chapter 8 that explains how to define functions in Python, including syntax and structure.</data>
      <data key="d3">2</data>
      <data key="d4">[]</data>
    </node>
    <node id="8.1.1 向函数传递信息">
      <data key="d0">8.1.1 向函数传递信息</data>
      <data key="d1">EVENT</data>
      <data key="d2">Section 8.1.1, "Passing Information to Functions," is a subsection that discusses how to provide input to functions in Python through parameters and arguments.</data>
      <data key="d3">2</data>
      <data key="d4">[]</data>
    </node>
    <node id="8.1.2 实参和形参">
      <data key="d0">8.1.2 实参和形参</data>
      <data key="d1">EVENT</data>
      <data key="d2">Section 8.1.2, "Arguments and Parameters," is a subsection that explains the difference between parameters (in function definitions) and arguments (in function calls) in Python.</data>
      <data key="d3">2</data>
      <data key="d4">[]</data>
    </node>
    <node id="函数">
      <data key="d0">函数</data>
      <data key="d1">EVENT</data>
      <data key="d2">A function is a named block of code in Python designed to perform a specific task, which can be defined, called, and reused throughout a program.</data>
      <data key="d3">4</data>
      <data key="d4">[]</data>
    </node>
    <node id="函数定义">
      <data key="d0">函数定义</data>
      <data key="d1">EVENT</data>
      <data key="d2">Function definition is the process of creating a new function in Python using the def keyword, specifying its name, parameters, and body.</data>
      <data key="d3">3</data>
      <data key="d4">[]</data>
    </node>
    <node id="函数调用">
      <data key="d0">函数调用</data>
      <data key="d1">EVENT</data>
      <data key="d2">Function call is the act of executing a function in Python by using its name and providing any required arguments.</data>
      <data key="d3">1</data>
      <data key="d4">[]</data>
    </node>
    <node id="文档字符串">
      <data key="d0">文档字符串</data>
      <data key="d1">EVENT</data>
      <data key="d2">A docstring is a special string literal in Python that appears right after the function definition and is used to document the function’s purpose and behavior.</data>
      <data key="d3">1</data>
      <data key="d4">[]</data>
    </node>
    <node id="PRINT">
      <data key="d0">PRINT</data>
      <data key="d1">EVENT</data>
      <data key="d2">print is a built-in Python function used to output text or other information to the console.</data>
      <data key="d3">1</data>
      <data key="d4">[]</data>
    </node>
    <node id="DEF">
      <data key="d0">DEF</data>
      <data key="d1">EVENT</data>
      <data key="d2">def is a Python keyword used to define a new function.</data>
      <data key="d3">1</data>
      <data key="d4">[]</data>
    </node>
    <node id="主程序文件">
      <data key="d0">主程序文件</data>
      <data key="d1">ORGANIZATION</data>
      <data key="d2">The main program file is the primary Python script that serves as the entry point for running the program, which can import and use functions from modules.</data>
      <data key="d3">1</data>
      <data key="d4">[]</data>
    </node>
    <node id="第 8 章">
      <data key="d0">第 8 章</data>
      <data key="d1" />
      <data key="d2" />
      <data key="d3">3</data>
      <data key="d4">[]</data>
    </node>
    <node id="参数">
      <data key="d0">参数</data>
      <data key="d1" />
      <data key="d2" />
      <data key="d3">2</data>
      <data key="d4">[]</data>
    </node>
    <node id="ALICE IN WONDERLAND">
      <data key="d0">ALICE IN WONDERLAND</data>
      <data key="d1">EVENT</data>
      <data key="d2">Alice in Wonderland is the title of a book used as an example in the favorite_book() function, illustrating how to pass a book title as an argument to a function.</data>
      <data key="d3">1</data>
      <data key="d4">[]</data>
    </node>
    <node id="HARRY">
      <data key="d0">HARRY</data>
      <data key="d1">PERSON</data>
      <data key="d2">Harry is the name of a hamster that serves as an example pet in the describe_pet() function, which is commonly used to demonstrate how to pass pet information as arguments to a function in Python. In the example code, Harry is specifically mentioned as a pet hamster, illustrating the process of providing arguments to a function that describes pets. Through the use of Harry, learners can better understand how to structure and utilize functions in Python programming by passing relevant details, such as the pet's name and type, as arguments.</data>
      <data key="d3">4</data>
      <data key="d4">[]</data>
    </node>
    <node id="WILLIE">
      <data key="d0">WILLIE</data>
      <data key="d1">PERSON</data>
      <data key="d2">Willie is the name of a dog used as an example pet in the describe_pet() function, which is commonly featured in Python programming tutorials. In these examples, Willie serves as a sample pet to demonstrate how to pass different pet names and types as arguments to a function. Specifically, Willie is referenced as a pet dog in the example code, illustrating the process of providing arguments to a function designed to describe pets. This use of Willie helps learners understand how to work with functions and arguments in Python by providing a clear, relatable example.</data>
      <data key="d3">4</data>
      <data key="d4">[]</data>
    </node>
    <node id="FAVORITE_BOOK">
      <data key="d0">FAVORITE_BOOK</data>
      <data key="d1">EVENT</data>
      <data key="d2">Favorite_book is the name of a function defined in the text as an exercise, which takes a parameter 'title' and prints a message about a favorite book, used to demonstrate passing arguments to functions in Python.</data>
      <data key="d3">2</data>
      <data key="d4">[]</data>
    </node>
    <node id="DESCRIBE_PET">
      <data key="d0">DESCRIBE_PET</data>
      <data key="d1">EVENT</data>
      <data key="d2">The "describe_pet" (also referred to as "Describe_pet") is a Python function defined in the provided text to illustrate the use of function parameters and arguments. This function takes two parameters: animal_type and pet_name. When called, it prints or displays information about a pet, utilizing the values provided for animal_type and pet_name. The function is used in examples to demonstrate how to define a function with parameters and how to call it with specific arguments in Python programming. Overall, "describe_pet" serves as an educational tool to help users understand the basics of function definition and invocation in Python, specifically in the context of handling and displaying information about pets.</data>
      <data key="d3">5</data>
      <data key="d4">[]</data>
    </node>
    <node id="PETS.PY">
      <data key="d0">PETS.PY</data>
      <data key="d1">EVENT</data>
      <data key="d2">PETS.PY is a Python script file referenced in instructional materials as an example for learning about function arguments and parameters. The script contains the definition and usage of the describe_pet() function, which is used to demonstrate how function calls and argument passing work in Python. PETS.PY serves as a practical example for learners to understand the concepts of defining functions, specifying parameters, and providing arguments when calling functions. The file is commonly used in educational contexts to illustrate these fundamental programming concepts.</data>
      <data key="d3">4</data>
      <data key="d4">[]</data>
    </node>
    <node id="CHAPTER">
      <data key="d0">CHAPTER</data>
      <data key="d1">EVENT</data>
      <data key="d2">Chapter refers to the current section or topic of the instructional text, which is about functions, parameters, and arguments in Python programming.</data>
      <data key="d3">3</data>
      <data key="d4">[]</data>
    </node>
    <node id="DISPLAY_MESSAGE">
      <data key="d0">DISPLAY_MESSAGE</data>
      <data key="d1" />
      <data key="d2" />
      <data key="d3">2</data>
      <data key="d4">[]</data>
    </node>
    <node id="DOG">
      <data key="d0">DOG</data>
      <data key="d1">PERSON</data>
      <data key="d2">Dog is an animal type used as an example in the Python code, specifically as the species of the pet named Willie.</data>
      <data key="d3">1</data>
      <data key="d4">[]</data>
    </node>
    <node id="ANIMAL_TYPE">
      <data key="d0">ANIMAL_TYPE</data>
      <data key="d1">EVENT</data>
      <data key="d2">animal_type is a function parameter in the Python examples, representing the type of animal being described (e.g., 'dog', 'hamster').</data>
      <data key="d3">2</data>
      <data key="d4">[]</data>
    </node>
    <node id="PET_NAME">
      <data key="d0">PET_NAME</data>
      <data key="d1">EVENT</data>
      <data key="d2">pet_name is a function parameter in the Python examples, representing the name of the pet being described.</data>
      <data key="d3">2</data>
      <data key="d4">[]</data>
    </node>
    <node id="HAMSTER">
      <data key="d0">HAMSTER</data>
      <data key="d1" />
      <data key="d2" />
      <data key="d3">1</data>
      <data key="d4">[]</data>
    </node>
    <node id="JIMI HENDRIX">
      <data key="d0">JIMI HENDRIX</data>
      <data key="d1">PERSON</data>
      <data key="d2">Jimi Hendrix is a person whose name is used as an example in the text to demonstrate how to format and print names using Python functions. He is referenced as a musician and his name is used in code examples for output formatting.</data>
      <data key="d3">1</data>
      <data key="d4">[]</data>
    </node>
    <node id="JOHN LEE HOOKER">
      <data key="d0">JOHN LEE HOOKER</data>
      <data key="d1">PERSON</data>
      <data key="d2">John Lee Hooker is a person whose name is used as an example in the text to demonstrate how to format and print names using Python functions. His full name, including a middle name, is used in code examples for output formatting.</data>
      <data key="d3">2</data>
      <data key="d4">[]</data>
    </node>
    <node id="JOHN HOOKER">
      <data key="d0">JOHN HOOKER</data>
      <data key="d1">PERSON</data>
      <data key="d2">John Hooker is a person whose name is used as an example in the text to demonstrate how to format and print names using Python functions, specifically in the context of handling optional middle names.</data>
      <data key="d3">1</data>
      <data key="d4">[]</data>
    </node>
    <node id="GET_FORMATTED_NAME">
      <data key="d0">GET_FORMATTED_NAME</data>
      <data key="d1">EVENT</data>
      <data key="d2">get_formatted_name is a Python function described in the text, which formats a person's name by combining first, (optional) middle, and last names into a standard format. It is used as an example to teach function arguments and default values.</data>
      <data key="d3">10</data>
      <data key="d4">[]</data>
    </node>
    <node id="LEE">
      <data key="d0">LEE</data>
      <data key="d1">PERSON</data>
      <data key="d2">Lee is used as a middle name in the example 'John Lee Hooker' to demonstrate how the get_formatted_name function handles optional middle names.</data>
      <data key="d3">1</data>
      <data key="d4">[]</data>
    </node>
    <node id="FIRST_NAME">
      <data key="d0">FIRST_NAME</data>
      <data key="d1">PERSON</data>
      <data key="d2">First_name is a parameter in the get_formatted_name function, representing the given name of a person in the code examples.</data>
      <data key="d3">1</data>
      <data key="d4">[]</data>
    </node>
    <node id="MIDDLE_NAME">
      <data key="d0">MIDDLE_NAME</data>
      <data key="d1">PERSON</data>
      <data key="d2">Middle_name is a parameter in the get_formatted_name function, representing the optional middle name of a person in the code examples.</data>
      <data key="d3">1</data>
      <data key="d4">[]</data>
    </node>
    <node id="LAST_NAME">
      <data key="d0">LAST_NAME</data>
      <data key="d1">PERSON</data>
      <data key="d2">Last_name is a parameter in the get_formatted_name function, representing the family name of a person in the code examples.</data>
      <data key="d3">1</data>
      <data key="d4">[]</data>
    </node>
    <node id="STANDARD FORMAT">
      <data key="d0">STANDARD FORMAT</data>
      <data key="d1">EVENT</data>
      <data key="d2">Standard format refers to the output format of names as demonstrated in the code examples, where names are capitalized and properly spaced.</data>
      <data key="d3">1</data>
      <data key="d4">[]</data>
    </node>
    <node id="CODE EXAMPLE">
      <data key="d0">CODE EXAMPLE</data>
      <data key="d1">EVENT</data>
      <data key="d2">Code example refers to the Python code snippets provided in the text to illustrate how to use the get_formatted_name function and print formatted names.</data>
      <data key="d3">2</data>
      <data key="d4">[]</data>
    </node>
    <node id="MUSICIAN">
      <data key="d0">MUSICIAN</data>
      <data key="d1" />
      <data key="d2" />
      <data key="d3">1</data>
      <data key="d4">[]</data>
    </node>
    <node id="MAKE_PIZZA">
      <data key="d0">MAKE_PIZZA</data>
      <data key="d1">ORGANIZATION</data>
      <data key="d2">make_pizza is a function in the pizza module, used to create pizzas with specified sizes and toppings. It is frequently used as an example in Python programming to demonstrate function import and aliasing techniques.</data>
      <data key="d3">8</data>
      <data key="d4">[]</data>
    </node>
    <node id="PIZZA">
      <data key="d0">PIZZA</data>
      <data key="d1">ORGANIZATION</data>
      <data key="d2">pizza is a Python module that contains the make_pizza function and possibly other related functions. It is used as an example module for demonstrating import statements, aliasing, and function usage in Python.</data>
      <data key="d3">3</data>
      <data key="d4">[]</data>
    </node>
    <node id="MP">
      <data key="d0">MP</data>
      <data key="d1">ORGANIZATION</data>
      <data key="d2">mp is an alias for the make_pizza function, created using the 'as' keyword in Python import statements to simplify function calls and avoid naming conflicts.</data>
      <data key="d3">2</data>
      <data key="d4">[]</data>
    </node>
    <node id="P">
      <data key="d0">P</data>
      <data key="d1">ORGANIZATION</data>
      <data key="d2">p is an alias for the pizza module, created using the 'as' keyword in Python import statements to make module function calls more concise.</data>
      <data key="d3">2</data>
      <data key="d4">[]</data>
    </node>
    <node id="MODULE_NAME">
      <data key="d0">MODULE_NAME</data>
      <data key="d1">ORGANIZATION</data>
      <data key="d2">module_name is a placeholder representing any Python module, used in example import statements to illustrate syntax for importing functions or modules with aliases or wildcards.</data>
      <data key="d3">3</data>
      <data key="d4">[]</data>
    </node>
    <node id="FUNCTION_NAME">
      <data key="d0">FUNCTION_NAME</data>
      <data key="d1">ORGANIZATION</data>
      <data key="d2">function_name is a placeholder representing any function within a Python module, used in example import statements to illustrate syntax for importing functions with aliases.</data>
      <data key="d3">1</data>
      <data key="d4">[]</data>
    </node>
    <node id="FN">
      <data key="d0">FN</data>
      <data key="d1">ORGANIZATION</data>
      <data key="d2">fn is an alias for a function, used as a placeholder in example import statements to demonstrate how to rename functions upon import.</data>
      <data key="d3">1</data>
      <data key="d4">[]</data>
    </node>
    <node id="MN">
      <data key="d0">MN</data>
      <data key="d1">ORGANIZATION</data>
      <data key="d2">mn is an alias for a module, used as a placeholder in example import statements to demonstrate how to rename modules upon import.</data>
      <data key="d3">1</data>
      <data key="d4">[]</data>
    </node>
    <node id="IMPORTING FUNCTIONS WITH ALIAS">
      <data key="d0">IMPORTING FUNCTIONS WITH ALIAS</data>
      <data key="d1" />
      <data key="d2" />
      <data key="d3">2</data>
      <data key="d4">[]</data>
    </node>
    <node id="IMPORTING MODULES WITH ALIAS">
      <data key="d0">IMPORTING MODULES WITH ALIAS</data>
      <data key="d1" />
      <data key="d2" />
      <data key="d3">2</data>
      <data key="d4">[]</data>
    </node>
    <node id="WILDCARD IMPORT">
      <data key="d0">WILDCARD IMPORT</data>
      <data key="d1" />
      <data key="d2" />
      <data key="d3">2</data>
      <data key="d4">[]</data>
    </node>
    <node id="FUNCTION NAMING GUIDELINES">
      <data key="d0">FUNCTION NAMING GUIDELINES</data>
      <data key="d1" />
      <data key="d2" />
      <data key="d3">1</data>
      <data key="d4">[]</data>
    </node>
    <node id="MUSHROOMS">
      <data key="d0">MUSHROOMS</data>
      <data key="d1">EVENT</data>
      <data key="d2">Mushrooms are used as an example pizza topping in the make_pizza function calls, representing a possible event or action within the context of pizza creation.</data>
      <data key="d3">1</data>
      <data key="d4">[]</data>
    </node>
    <node id="GREEN PEPPERS">
      <data key="d0">GREEN PEPPERS</data>
      <data key="d1">EVENT</data>
      <data key="d2">Green peppers are used as an example pizza topping in the make_pizza function calls, representing a possible event or action within the context of pizza creation.</data>
      <data key="d3">1</data>
      <data key="d4">[]</data>
    </node>
    <node id="EXTRA CHEESE">
      <data key="d0">EXTRA CHEESE</data>
      <data key="d1">EVENT</data>
      <data key="d2">Extra cheese is used as an example pizza topping in the make_pizza function calls, representing a possible event or action within the context of pizza creation.</data>
      <data key="d3">1</data>
      <data key="d4">[]</data>
    </node>
    <node id="PEPPERONI">
      <data key="d0">PEPPERONI</data>
      <data key="d1" />
      <data key="d2" />
      <data key="d3">1</data>
      <data key="d4">[]</data>
    </node>
    <node id="第 9 章">
      <data key="d0">第 9 章</data>
      <data key="d1">EVENT</data>
      <data key="d2">Chapter 9 is a section in a book or instructional material where the reader will learn about writing classes, which encapsulate functions and data for flexible and efficient use.</data>
      <data key="d3">0</data>
      <data key="d4">[]</data>
    </node>
    <edge source="PYTHON" target="GREETER.PY">
      <data key="d5">8.0</data>
      <data key="d6">greeter.py is a Python script, written and executed in the Python programming language</data>
      <data key="d7">0</data>
    </edge>
    <edge source="PYTHON" target="GREET_USER">
      <data key="d5">8.0</data>
      <data key="d6">greet_user is a function written in Python and executed by the Python interpreter</data>
      <data key="d7">0</data>
    </edge>
    <edge source="PYTHON" target="MODULE">
      <data key="d5">8.0</data>
      <data key="d6">A module is a Python file that can be imported into other Python programs</data>
      <data key="d7">0</data>
    </edge>
    <edge source="PYTHON" target="JESSE">
      <data key="d5">7.0</data>
      <data key="d6">Jesse is used as an example argument in a Python function to demonstrate how to pass information to a function parameter.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="PYTHON" target="ALICE IN WONDERLAND">
      <data key="d5">6.0</data>
      <data key="d6">Alice in Wonderland is used as an example book title in a Python function to illustrate passing arguments.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="PYTHON" target="HARRY">
      <data key="d5">13.0</data>
      <data key="d6">Harry is an example pet name commonly used in Python code to demonstrate how function argument passing works. In Python programming tutorials and examples, "Harry" is often chosen as the name of a pet within functions to illustrate the concept of passing arguments to functions. This helps learners understand how data can be provided to functions and how those functions can operate on the provided arguments. The use of "Harry" as a sample pet name is a teaching tool within the context of Python, making abstract programming concepts more relatable and easier to grasp.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="PYTHON" target="WILLIE">
      <data key="d5">13.0</data>
      <data key="d6">Willie is commonly used as an example pet name in Python programming to demonstrate how function argument passing works. In Python code examples, Willie often appears as a sample argument in functions related to pets, helping illustrate the concept of passing values to functions. This usage of Willie serves as a clear and relatable way for learners to understand how arguments are handled in Python functions.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="PYTHON" target="DISPLAY_MESSAGE">
      <data key="d5">7.0</data>
      <data key="d6">Display_message is a function written in Python to demonstrate function definition and invocation.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="PYTHON" target="FAVORITE_BOOK">
      <data key="d5">7.0</data>
      <data key="d6">Favorite_book is a function written in Python to demonstrate passing arguments to functions.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="PYTHON" target="DESCRIBE_PET">
      <data key="d5">7.0</data>
      <data key="d6">Describe_pet is a function written in Python to demonstrate the use of multiple parameters and arguments.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="PYTHON" target="PETS.PY">
      <data key="d5">16.0</data>
      <data key="d6">PYTHON is a widely used programming language known for its readability and versatility. PETS.PY is a Python script file that contains examples of functions, specifically designed to demonstrate how function argument passing and default values work in Python. This script serves as an educational resource, illustrating key concepts related to defining and calling functions, handling arguments, and utilizing default parameter values within the Python programming language.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="PYTHON" target="ANIMAL_TYPE">
      <data key="d5">7.0</data>
      <data key="d6">animal_type is a parameter used in Python function examples.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="PYTHON" target="PET_NAME">
      <data key="d5">1.0</data>
      <data key="d6">pet_name is a parameter used in Python function examples.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="PYTHON" target="GET_FORMATTED_NAME">
      <data key="d5">1.0</data>
      <data key="d6">get_formatted_name is a function written in the Python programming language, used in the text to illustrate Python's handling of function arguments and default values.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="PYTHON" target="CODE EXAMPLE">
      <data key="d5">1.0</data>
      <data key="d6">The code examples are written in the Python programming language.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="PYTHON" target="MAKE_PIZZA">
      <data key="d5">7.0</data>
      <data key="d6">make_pizza is a function used in Python programming examples.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="PYTHON" target="PIZZA">
      <data key="d5">7.0</data>
      <data key="d6">pizza is a module used in Python programming to demonstrate import and aliasing techniques.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="PYTHON" target="IMPORTING FUNCTIONS WITH ALIAS">
      <data key="d5">8.0</data>
      <data key="d6">Importing functions with alias is a feature of Python's import system.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="PYTHON" target="IMPORTING MODULES WITH ALIAS">
      <data key="d5">8.0</data>
      <data key="d6">Importing modules with alias is a feature of Python's import system.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="PYTHON" target="WILDCARD IMPORT">
      <data key="d5">8.0</data>
      <data key="d6">Wildcard import is a feature of Python's import system.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="PYTHON" target="FUNCTION NAMING GUIDELINES">
      <data key="d5">7.0</data>
      <data key="d6">Function naming guidelines are part of Python's best practices for code clarity.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="GREETER.PY" target="GREET_USER">
      <data key="d5">9.0</data>
      <data key="d6">The greeter.py file contains the definition and implementation of the greet_user function</data>
      <data key="d7">0</data>
    </edge>
    <edge source="GREETER.PY" target="MODULE">
      <data key="d5">1.0</data>
      <data key="d6">greeter.py is an example of a Python module containing function definitions</data>
      <data key="d7">0</data>
    </edge>
    <edge source="GREET_USER" target="USERNAME">
      <data key="d5">9.0</data>
      <data key="d6">The greet_user function takes username as a parameter to personalize the greeting</data>
      <data key="d7">0</data>
    </edge>
    <edge source="GREET_USER" target="JESSE">
      <data key="d5">7.0</data>
      <data key="d6">Jesse is an example value passed as the username parameter to the greet_user function</data>
      <data key="d7">0</data>
    </edge>
    <edge source="GREET_USER" target="SARAH">
      <data key="d5">7.0</data>
      <data key="d6">Sarah is an example value passed as the username parameter to the greet_user function</data>
      <data key="d7">0</data>
    </edge>
    <edge source="MODULE" target="主程序文件">
      <data key="d5">1.0</data>
      <data key="d6">The main program file can import and use functions from modules</data>
      <data key="d7">0</data>
    </edge>
    <edge source="8.1 定义函数" target="第 8 章">
      <data key="d5">9.0</data>
      <data key="d6">Section 8.1 is a subsection of Chapter 8, focusing on defining functions</data>
      <data key="d7">0</data>
    </edge>
    <edge source="8.1 定义函数" target="函数定义">
      <data key="d5">9.0</data>
      <data key="d6">Section 8.1 explains the concept and process of function definition in Python</data>
      <data key="d7">0</data>
    </edge>
    <edge source="8.1.1 向函数传递信息" target="第 8 章">
      <data key="d5">9.0</data>
      <data key="d6">Section 8.1.1 is a subsection of Chapter 8, focusing on passing information to functions</data>
      <data key="d7">0</data>
    </edge>
    <edge source="8.1.1 向函数传递信息" target="参数">
      <data key="d5">8.0</data>
      <data key="d6">Section 8.1.1 discusses how to pass parameters to functions</data>
      <data key="d7">0</data>
    </edge>
    <edge source="8.1.2 实参和形参" target="第 8 章">
      <data key="d5">9.0</data>
      <data key="d6">Section 8.1.2 is a subsection of Chapter 8, focusing on arguments and parameters</data>
      <data key="d7">0</data>
    </edge>
    <edge source="8.1.2 实参和形参" target="参数">
      <data key="d5">8.0</data>
      <data key="d6">Section 8.1.2 explains the difference between arguments and parameters</data>
      <data key="d7">0</data>
    </edge>
    <edge source="函数" target="函数定义">
      <data key="d5">9.0</data>
      <data key="d6">A function is created through function definition</data>
      <data key="d7">0</data>
    </edge>
    <edge source="函数" target="函数调用">
      <data key="d5">9.0</data>
      <data key="d6">A function is executed through a function call</data>
      <data key="d7">0</data>
    </edge>
    <edge source="函数" target="文档字符串">
      <data key="d5">8.0</data>
      <data key="d6">A function can have a docstring to describe its purpose</data>
      <data key="d7">0</data>
    </edge>
    <edge source="函数" target="PRINT">
      <data key="d5">7.0</data>
      <data key="d6">The print function is often used within user-defined functions to display output</data>
      <data key="d7">0</data>
    </edge>
    <edge source="函数定义" target="DEF">
      <data key="d5">9.0</data>
      <data key="d6">The def keyword is used in Python to define a function</data>
      <data key="d7">0</data>
    </edge>
    <edge source="HARRY" target="WILLIE">
      <data key="d5">1.0</data>
      <data key="d6">Harry and Willie are both example pet names used in the describe_pet() function to show multiple function calls with different arguments.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="HARRY" target="PETS.PY">
      <data key="d5">1.0</data>
      <data key="d6">Harry is a pet described in the pets.py example.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="HARRY" target="HAMSTER">
      <data key="d5">10.0</data>
      <data key="d6">Harry is a hamster, as shown in the example code.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="WILLIE" target="PETS.PY">
      <data key="d5">6.0</data>
      <data key="d6">Willie is a pet described in the pets.py example.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="WILLIE" target="DOG">
      <data key="d5">10.0</data>
      <data key="d6">Willie is a dog, as shown in the example code.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="FAVORITE_BOOK" target="CHAPTER">
      <data key="d5">6.0</data>
      <data key="d6">Favorite_book is an exercise function meant to illustrate the topic of the chapter.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="DESCRIBE_PET" target="PETS.PY">
      <data key="d5">8.0</data>
      <data key="d6">The describe_pet function is defined and called in the pets.py script.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="DESCRIBE_PET" target="CHAPTER">
      <data key="d5">1.0</data>
      <data key="d6">Describe_pet is an exercise function meant to illustrate the topic of the chapter.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="DESCRIBE_PET" target="ANIMAL_TYPE">
      <data key="d5">9.0</data>
      <data key="d6">The describe_pet function takes animal_type as a parameter to specify the species of the pet.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="DESCRIBE_PET" target="PET_NAME">
      <data key="d5">9.0</data>
      <data key="d6">The describe_pet function takes pet_name as a parameter to specify the name of the pet.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="CHAPTER" target="DISPLAY_MESSAGE">
      <data key="d5">6.0</data>
      <data key="d6">Display_message is an exercise function meant to illustrate the topic of the chapter.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="JIMI HENDRIX" target="GET_FORMATTED_NAME">
      <data key="d5">8.0</data>
      <data key="d6">Jimi Hendrix's name is used as an example input and output for the get_formatted_name function.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="JOHN LEE HOOKER" target="GET_FORMATTED_NAME">
      <data key="d5">8.0</data>
      <data key="d6">John Lee Hooker's name is used as an example input and output for the get_formatted_name function, demonstrating the use of a middle name.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="JOHN LEE HOOKER" target="LEE">
      <data key="d5">8.0</data>
      <data key="d6">Lee is the middle name in the full name John Lee Hooker, as used in the code example.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="JOHN HOOKER" target="GET_FORMATTED_NAME">
      <data key="d5">8.0</data>
      <data key="d6">John Hooker's name is used as an example input and output for the get_formatted_name function, demonstrating the case without a middle name.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="GET_FORMATTED_NAME" target="MUSICIAN">
      <data key="d5">9.0</data>
      <data key="d6">The variable musician is assigned the value returned by the get_formatted_name function in the code examples.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="GET_FORMATTED_NAME" target="FIRST_NAME">
      <data key="d5">9.0</data>
      <data key="d6">First_name is a parameter of the get_formatted_name function, representing the first part of a person's name.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="GET_FORMATTED_NAME" target="MIDDLE_NAME">
      <data key="d5">9.0</data>
      <data key="d6">Middle_name is a parameter of the get_formatted_name function, representing the optional middle part of a person's name.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="GET_FORMATTED_NAME" target="LAST_NAME">
      <data key="d5">9.0</data>
      <data key="d6">Last_name is a parameter of the get_formatted_name function, representing the last part of a person's name.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="GET_FORMATTED_NAME" target="STANDARD FORMAT">
      <data key="d5">8.0</data>
      <data key="d6">The get_formatted_name function returns names in a standard format, as described in the text.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="GET_FORMATTED_NAME" target="CODE EXAMPLE">
      <data key="d5">8.0</data>
      <data key="d6">The code examples in the text demonstrate the use of the get_formatted_name function.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="MAKE_PIZZA" target="PIZZA">
      <data key="d5">10.0</data>
      <data key="d6">make_pizza is a function defined within the pizza module.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="MAKE_PIZZA" target="MP">
      <data key="d5">9.0</data>
      <data key="d6">mp is an alias for the make_pizza function, created to simplify function calls.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="MAKE_PIZZA" target="WILDCARD IMPORT">
      <data key="d5">1.0</data>
      <data key="d6">Wildcard import allows direct use of make_pizza without module or alias prefix.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="MAKE_PIZZA" target="PEPPERONI">
      <data key="d5">6.0</data>
      <data key="d6">make_pizza is called with pepperoni as a topping in the example.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="MAKE_PIZZA" target="MUSHROOMS">
      <data key="d5">6.0</data>
      <data key="d6">make_pizza is called with mushrooms as a topping in the example.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="MAKE_PIZZA" target="GREEN PEPPERS">
      <data key="d5">6.0</data>
      <data key="d6">make_pizza is called with green peppers as a topping in the example.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="MAKE_PIZZA" target="EXTRA CHEESE">
      <data key="d5">1.0</data>
      <data key="d6">make_pizza is called with extra cheese as a topping in the example.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="PIZZA" target="P">
      <data key="d5">9.0</data>
      <data key="d6">p is an alias for the pizza module, created to make function calls more concise.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="MP" target="IMPORTING FUNCTIONS WITH ALIAS">
      <data key="d5">8.0</data>
      <data key="d6">mp is an example of importing a function with an alias.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="P" target="IMPORTING MODULES WITH ALIAS">
      <data key="d5">8.0</data>
      <data key="d6">p is an example of importing a module with an alias.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="MODULE_NAME" target="FUNCTION_NAME">
      <data key="d5">6.0</data>
      <data key="d6">function_name is a function within module_name, used as a placeholder in import examples.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="MODULE_NAME" target="FN">
      <data key="d5">6.0</data>
      <data key="d6">fn is an alias for function_name in module_name, used in import examples.</data>
      <data key="d7">0</data>
    </edge>
    <edge source="MODULE_NAME" target="MN">
      <data key="d5">6.0</data>
      <data key="d6">mn is an alias for module_name, used in import examples.</data>
      <data key="d7">0</data>
    </edge>
  </graph>
</graphml>
