import pandas as pd
import networkx as nx
from pathlib import Path
import xml.etree.ElementTree as ET

def export_to_gephi_utf8(output_dir="output"):
    """将GraphRAG输出转换为UTF-8编码的Gephi格式"""
    
    print("正在读取GraphRAG输出数据...")
    
    # 读取实体和关系数据
    entities_df = pd.read_parquet(f"{output_dir}/entities.parquet")
    relationships_df = pd.read_parquet(f"{output_dir}/relationships.parquet")
    
    print(f"找到 {len(entities_df)} 个实体和 {len(relationships_df)} 个关系")
    
    # 创建NetworkX图
    G = nx.Graph()
    
    # 添加节点，确保中文字符正确处理
    for _, entity in entities_df.iterrows():
        title = str(entity['title']).strip()
        description = str(entity.get('description', '')).strip()
        
        G.add_node(
            title,
            label=title,
            type=str(entity.get('type', 'entity')),
            description=description,
            degree=int(entity.get('degree', 0)),
            community_ids=str(entity.get('community_ids', []))
        )
    
    # 添加边
    for _, rel in relationships_df.iterrows():
        source = str(rel['source']).strip()
        target = str(rel['target']).strip()
        
        if source in G.nodes() and target in G.nodes():
            G.add_edge(
                source,
                target,
                weight=float(rel.get('weight', 1.0)),
                description=str(rel.get('description', '')).strip(),
                rank=int(rel.get('rank', 0))
            )
    
    # 导出为GEXF格式，明确指定UTF-8编码
    gexf_path = f"{output_dir}/knowledge_graph_utf8.gexf"
    
    # 使用自定义方法确保UTF-8编码
    with open(gexf_path, 'w', encoding='utf-8') as f:
        # 写入XML声明
        f.write('<?xml version="1.0" encoding="UTF-8"?>\n')
        
        # 使用NetworkX生成GEXF内容，但跳过XML声明
        gexf_content = '\n'.join(nx.generate_gexf(G))[39:]  # 跳过默认的XML声明
        f.write(gexf_content)
    
    # 也导出GraphML格式
    graphml_path = f"{output_dir}/knowledge_graph_utf8.graphml"
    nx.write_graphml(G, graphml_path, encoding='utf-8')
    
    print(f"UTF-8编码图谱已导出到:")
    print(f"  - GEXF格式: {gexf_path}")
    print(f"  - GraphML格式: {graphml_path}")
    print(f"节点数: {G.number_of_nodes()}")
    print(f"边数: {G.number_of_edges()}")
    
    # 显示一些中文节点示例
    chinese_nodes = [node for node in G.nodes() if any('\u4e00' <= char <= '\u9fff' for char in node)]
    if chinese_nodes:
        print(f"\n中文节点示例:")
        for node in chinese_nodes[:5]:
            print(f"  - {node}")
    
    return gexf_path, graphml_path

if __name__ == "__main__":
    export_to_gephi_utf8()
