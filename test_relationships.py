import pandas as pd
import networkx as nx

def test_parquet_relationships():
    """测试当前parquet数据中节点与节点之间的关系"""
    
    print("🔍 测试 GraphRAG Parquet 数据中的节点关系")
    print("=" * 60)
    
    # 读取数据
    entities_df = pd.read_parquet('output/entities.parquet')
    relationships_df = pd.read_parquet('output/relationships.parquet')
    
    print(f"📊 数据概览:")
    print(f"  实体数量: {len(entities_df)}")
    print(f"  关系数量: {len(relationships_df)}")
    
    # 显示所有实体
    print(f"\n📋 所有实体列表:")
    for i, entity in entities_df.iterrows():
        print(f"  {i+1}. {entity['title']} (类型: {entity['type']})")
    
    # 显示所有关系
    print(f"\n🔗 所有关系列表:")
    if len(relationships_df) > 0:
        for i, rel in relationships_df.iterrows():
            source = rel['source']
            target = rel['target']
            description = rel['description'][:100] + '...' if len(str(rel['description'])) > 100 else rel['description']
            weight = rel.get('weight', 'N/A')
            print(f"  {i+1}. {source} → {target}")
            print(f"     描述: {description}")
            print(f"     权重: {weight}")
            print()
    else:
        print("  ❌ 没有找到任何关系")
    
    # 分析关系网络
    print(f"🕸️ 关系网络分析:")
    
    # 创建网络图
    G = nx.Graph()
    
    # 添加节点
    for _, entity in entities_df.iterrows():
        G.add_node(entity['title'], type=entity['type'])
    
    # 添加边
    edge_count = 0
    for _, rel in relationships_df.iterrows():
        source = rel['source']
        target = rel['target']
        if source in G.nodes() and target in G.nodes():
            G.add_edge(source, target, 
                      description=rel['description'],
                      weight=rel.get('weight', 1.0))
            edge_count += 1
    
    print(f"  网络节点数: {G.number_of_nodes()}")
    print(f"  网络边数: {G.number_of_edges()}")
    print(f"  连通性: {'连通' if nx.is_connected(G) else '不连通'}")
    
    # 分析每个节点的连接度
    print(f"\n📈 节点连接度分析:")
    degrees = dict(G.degree())
    sorted_degrees = sorted(degrees.items(), key=lambda x: x[1], reverse=True)
    
    for node, degree in sorted_degrees:
        neighbors = list(G.neighbors(node))
        print(f"  {node}: {degree} 个连接")
        if neighbors:
            print(f"    连接到: {', '.join(neighbors)}")
        else:
            print(f"    ❌ 孤立节点")
        print()
    
    # 检查是否有孤立节点
    isolated_nodes = list(nx.isolates(G))
    if isolated_nodes:
        print(f"⚠️ 孤立节点 ({len(isolated_nodes)} 个):")
        for node in isolated_nodes:
            print(f"  - {node}")
    else:
        print(f"✅ 没有孤立节点，所有节点都有连接")
    
    # 分析关系类型
    print(f"\n🏷️ 关系类型分析:")
    if len(relationships_df) > 0:
        # 尝试从描述中提取关系类型
        relationship_types = {}
        for _, rel in relationships_df.iterrows():
            desc = str(rel['description']).lower()
            # 简单的关系类型识别
            if '包含' in desc or 'contains' in desc:
                rel_type = '包含关系'
            elif '对应' in desc or 'corresponds' in desc:
                rel_type = '对应关系'
            elif '分为' in desc or 'category' in desc:
                rel_type = '分类关系'
            elif '可有' in desc or 'feature' in desc:
                rel_type = '特征关系'
            elif '使用' in desc or 'uses' in desc:
                rel_type = '使用关系'
            elif '处理' in desc or 'processes' in desc:
                rel_type = '处理关系'
            else:
                rel_type = '其他关系'
            
            relationship_types[rel_type] = relationship_types.get(rel_type, 0) + 1
        
        for rel_type, count in relationship_types.items():
            print(f"  {rel_type}: {count} 个")
    
    # 生成关系矩阵
    print(f"\n📊 关系矩阵 (节点间是否有直接连接):")
    nodes = list(G.nodes())
    print("     ", end="")
    for i, node in enumerate(nodes):
        print(f"{i+1:2d}", end=" ")
    print()
    
    for i, node1 in enumerate(nodes):
        print(f"{i+1:2d}. {node1[:8]:8s}", end=" ")
        for j, node2 in enumerate(nodes):
            if i == j:
                print(" ●", end=" ")  # 自己
            elif G.has_edge(node1, node2):
                print(" ✓", end=" ")  # 有连接
            else:
                print(" ○", end=" ")  # 无连接
        print()
    
    print(f"\n图例: ● = 自己, ✓ = 有连接, ○ = 无连接")
    
    return G

if __name__ == "__main__":
    graph = test_parquet_relationships()
    
    print(f"\n💡 总结:")
    print(f"  - 当前数据{'包含' if graph.number_of_edges() > 0 else '不包含'}节点间的关系")
    print(f"  - 网络连通性: {'良好' if nx.is_connected(graph) else '需要改进'}")
    print(f"  - 适合用于知识图谱可视化: {'是' if graph.number_of_edges() > 0 else '否'}")
