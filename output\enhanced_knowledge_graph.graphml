<?xml version='1.0' encoding='utf-8'?>
<graphml xmlns="http://graphml.graphdrawing.org/xmlns" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://graphml.graphdrawing.org/xmlns http://graphml.graphdrawing.org/xmlns/1.0/graphml.xsd">
  <key id="d7" for="edge" attr.name="description" attr.type="string" />
  <key id="d6" for="edge" attr.name="weight" attr.type="double" />
  <key id="d5" for="edge" attr.name="relationship" attr.type="string" />
  <key id="d4" for="node" attr.name="size" attr.type="long" />
  <key id="d3" for="node" attr.name="level" attr.type="long" />
  <key id="d2" for="node" attr.name="description" attr.type="string" />
  <key id="d1" for="node" attr.name="type" attr.type="string" />
  <key id="d0" for="node" attr.name="label" attr.type="string" />
  <graph edgedefault="undirected">
    <node id="函数">
      <data key="d0">函数</data>
      <data key="d1">core_concept</data>
      <data key="d2">Python中的可重用代码块，是程序的基本构建单元</data>
      <data key="d3">1</data>
      <data key="d4">50</data>
    </node>
    <node id="实参">
      <data key="d0">实参</data>
      <data key="d1">parameter_concept</data>
      <data key="d2">调用函数时传递的实际值</data>
      <data key="d3">2</data>
      <data key="d4">35</data>
    </node>
    <node id="形参">
      <data key="d0">形参</data>
      <data key="d1">parameter_concept</data>
      <data key="d2">函数定义时的参数名称</data>
      <data key="d3">2</data>
      <data key="d4">35</data>
    </node>
    <node id="位置实参">
      <data key="d0">位置实参</data>
      <data key="d1">parameter_type</data>
      <data key="d2">按照参数位置顺序传递的实参</data>
      <data key="d3">2</data>
      <data key="d4">25</data>
    </node>
    <node id="位置实参的顺序">
      <data key="d0">位置实参的顺序</data>
      <data key="d1">rule</data>
      <data key="d2">位置实参必须按照函数定义的参数顺序传递</data>
      <data key="d3">3</data>
      <data key="d4">20</data>
    </node>
    <node id="关键字实参">
      <data key="d0">关键字实参</data>
      <data key="d1">parameter_type</data>
      <data key="d2">使用参数名称传递的实参，不依赖位置顺序</data>
      <data key="d3">3</data>
      <data key="d4">25</data>
    </node>
    <node id="默认值">
      <data key="d0">默认值</data>
      <data key="d1">feature</data>
      <data key="d2">为形参设置的默认值，调用时可以省略该参数</data>
      <data key="d3">3</data>
      <data key="d4">25</data>
    </node>
    <node id="返回值">
      <data key="d0">返回值</data>
      <data key="d1">output</data>
      <data key="d2">函数执行完成后返回给调用者的值</data>
      <data key="d3">3</data>
      <data key="d4">20</data>
    </node>
    <node id="while循环">
      <data key="d0">while循环</data>
      <data key="d1">control_structure</data>
      <data key="d2">重复执行代码块直到条件为假的循环结构</data>
      <data key="d3">1</data>
      <data key="d4">35</data>
    </node>
    <node id="列表">
      <data key="d0">列表</data>
      <data key="d1">data_structure</data>
      <data key="d2">Python中存储多个有序元素的可变数据类型</data>
      <data key="d3">1</data>
      <data key="d4">35</data>
    </node>
    <edge source="函数" target="实参">
      <data key="d5">contains</data>
      <data key="d6">3.0</data>
      <data key="d7">函数包含实参</data>
    </edge>
    <edge source="函数" target="形参">
      <data key="d5">contains</data>
      <data key="d6">3.0</data>
      <data key="d7">函数包含形参</data>
    </edge>
    <edge source="函数" target="返回值">
      <data key="d5">contains</data>
      <data key="d6">3.0</data>
      <data key="d7">函数包含返回值</data>
    </edge>
    <edge source="函数" target="while循环">
      <data key="d5">used_with</data>
      <data key="d6">1.0</data>
      <data key="d7">函数中可以使用while循环</data>
    </edge>
    <edge source="实参" target="位置实参">
      <data key="d5">contains</data>
      <data key="d6">3.0</data>
      <data key="d7">实参包含位置实参</data>
    </edge>
    <edge source="实参" target="关键字实参">
      <data key="d5">contains</data>
      <data key="d6">3.0</data>
      <data key="d7">实参包含关键字实参</data>
    </edge>
    <edge source="实参" target="形参">
      <data key="d5">corresponds_to</data>
      <data key="d6">2.0</data>
      <data key="d7">实参对应形参</data>
    </edge>
    <edge source="形参" target="默认值">
      <data key="d5">contains</data>
      <data key="d6">3.0</data>
      <data key="d7">形参包含默认值</data>
    </edge>
    <edge source="位置实参" target="位置实参的顺序">
      <data key="d5">contains</data>
      <data key="d6">3.0</data>
      <data key="d7">位置实参包含位置实参的顺序</data>
    </edge>
    <edge source="位置实参" target="关键字实参">
      <data key="d5">alternative_to</data>
      <data key="d6">1.5</data>
      <data key="d7">位置实参和关键字实参是两种传参方式</data>
    </edge>
    <edge source="返回值" target="列表">
      <data key="d5">can_be</data>
      <data key="d6">1.0</data>
      <data key="d7">返回值可以是列表</data>
    </edge>
    <edge source="while循环" target="列表">
      <data key="d5">processes</data>
      <data key="d6">1.0</data>
      <data key="d7">while循环可以处理列表</data>
    </edge>
  </graph>
</graphml>
