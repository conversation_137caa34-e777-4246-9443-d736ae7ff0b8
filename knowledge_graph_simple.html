<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Python函数知识图谱</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
        }
        
        .controls {
            padding: 15px 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        .graph-container {
            width: 100%;
            height: 600px;
            background: #ffffff;
            position: relative;
            overflow: hidden;
        }
        
        .node {
            position: absolute;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 12px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            border: 2px solid white;
        }
        
        .node:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        }
        
        .edge {
            position: absolute;
            background: #95a5a6;
            transform-origin: left center;
            opacity: 0.7;
        }
        
        .info-panel {
            padding: 20px;
            background: #f8f9fa;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }
        
        .legend {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 50%;
        }
        
        .selected {
            border: 3px solid #007bff !important;
            box-shadow: 0 0 15px rgba(0,123,255,0.5) !important;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🐍 Python函数知识图谱</h1>
            <p>基于concepts.csv构建的交互式概念图谱</p>
        </div>
        
        <div class="controls">
            <button onclick="resetLayout()">重置布局</button>
            <button onclick="randomLayout()">随机布局</button>
            <button onclick="circleLayout()">圆形布局</button>
            <button onclick="exportImage()">导出图片</button>
        </div>
        
        <div class="graph-container" id="graphContainer"></div>
        
        <div class="info-panel">
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number" id="nodeCount">9</div>
                    <div class="stat-label">概念节点</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="edgeCount">7</div>
                    <div class="stat-label">关系连线</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="selectedNode">无</div>
                    <div class="stat-label">选中节点</div>
                </div>
            </div>
            
            <h4>🎨 节点颜色说明</h4>
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color" style="background: #e74c3c;"></div>
                    <span>核心概念</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #3498db;"></div>
                    <span>参数概念</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #2ecc71;"></div>
                    <span>参数类型</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #f39c12;"></div>
                    <span>输出概念</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #9b59b6;"></div>
                    <span>特性概念</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 图谱数据
        const graphData = {
            nodes: [
                { id: '函数', label: '函数', color: '#e74c3c', size: 60, type: 'core' },
                { id: '实参', label: '实参', color: '#3498db', size: 45, type: 'param' },
                { id: '形参', label: '形参', color: '#3498db', size: 45, type: 'param' },
                { id: '位置实参', label: '位置实参', color: '#2ecc71', size: 35, type: 'param_type' },
                { id: '关键字实参', label: '关键字实参', color: '#2ecc71', size: 35, type: 'param_type' },
                { id: '返回值', label: '返回值', color: '#f39c12', size: 40, type: 'output' },
                { id: '默认值', label: '默认值', color: '#9b59b6', size: 30, type: 'feature' },
                { id: 'while循环', label: 'while循环', color: '#27ae60', size: 40, type: 'control' },
                { id: '列表', label: '列表', color: '#f1c40f', size: 40, type: 'data' }
            ],
            edges: [
                { source: '函数', target: '实参', weight: 3 },
                { source: '函数', target: '形参', weight: 3 },
                { source: '函数', target: '返回值', weight: 3 },
                { source: '实参', target: '位置实参', weight: 2 },
                { source: '实参', target: '关键字实参', weight: 2 },
                { source: '形参', target: '默认值', weight: 2 },
                { source: '实参', target: '形参', weight: 2 }
            ]
        };
        
        let nodes = [];
        let edges = [];
        let selectedNode = null;
        
        // 初始化图谱
        function initGraph() {
            const container = document.getElementById('graphContainer');
            const containerRect = container.getBoundingClientRect();
            const centerX = containerRect.width / 2;
            const centerY = containerRect.height / 2;
            
            // 清空容器
            container.innerHTML = '';
            
            // 创建节点
            graphData.nodes.forEach((nodeData, index) => {
                const node = document.createElement('div');
                node.className = 'node';
                node.id = 'node-' + nodeData.id;
                node.style.width = nodeData.size + 'px';
                node.style.height = nodeData.size + 'px';
                node.style.backgroundColor = nodeData.color;
                node.style.fontSize = Math.max(10, nodeData.size / 4) + 'px';
                node.textContent = nodeData.label;
                
                // 初始位置（圆形排列）
                const angle = (index / graphData.nodes.length) * 2 * Math.PI;
                const radius = Math.min(centerX, centerY) * 0.6;
                const x = centerX + Math.cos(angle) * radius - nodeData.size / 2;
                const y = centerY + Math.sin(angle) * radius - nodeData.size / 2;
                
                node.style.left = x + 'px';
                node.style.top = y + 'px';
                
                // 添加点击事件
                node.addEventListener('click', () => selectNode(nodeData.id));
                
                // 添加拖拽功能
                makeDraggable(node);
                
                container.appendChild(node);
                nodes.push({ element: node, data: nodeData, x: x, y: y });
            });
            
            // 创建连线
            drawEdges();
            
            // 应用力导向布局
            setTimeout(() => {
                applyForceLayout();
            }, 100);
        }
        
        // 绘制连线
        function drawEdges() {
            const container = document.getElementById('graphContainer');
            
            // 移除现有连线
            const existingEdges = container.querySelectorAll('.edge');
            existingEdges.forEach(edge => edge.remove());
            
            graphData.edges.forEach(edgeData => {
                const sourceNode = nodes.find(n => n.data.id === edgeData.source);
                const targetNode = nodes.find(n => n.data.id === edgeData.target);
                
                if (sourceNode && targetNode) {
                    const edge = document.createElement('div');
                    edge.className = 'edge';
                    
                    const sourceRect = sourceNode.element.getBoundingClientRect();
                    const targetRect = targetNode.element.getBoundingClientRect();
                    const containerRect = container.getBoundingClientRect();
                    
                    const x1 = sourceRect.left - containerRect.left + sourceRect.width / 2;
                    const y1 = sourceRect.top - containerRect.top + sourceRect.height / 2;
                    const x2 = targetRect.left - containerRect.left + targetRect.width / 2;
                    const y2 = targetRect.top - containerRect.top + targetRect.height / 2;
                    
                    const length = Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2);
                    const angle = Math.atan2(y2 - y1, x2 - x1) * 180 / Math.PI;
                    
                    edge.style.width = length + 'px';
                    edge.style.height = Math.max(1, edgeData.weight) + 'px';
                    edge.style.left = x1 + 'px';
                    edge.style.top = y1 + 'px';
                    edge.style.transform = 
otate(deg);
                    
                    container.appendChild(edge);
                }
            });
        }
        
        // 选择节点
        function selectNode(nodeId) {
            // 移除之前的选择
            nodes.forEach(n => n.element.classList.remove('selected'));
            
            // 选择新节点
            const node = nodes.find(n => n.data.id === nodeId);
            if (node) {
                node.element.classList.add('selected');
                selectedNode = nodeId;
                document.getElementById('selectedNode').textContent = node.data.label;
            }
        }
        
        // 使元素可拖拽
        function makeDraggable(element) {
            let isDragging = false;
            let startX, startY, initialX, initialY;
            
            element.addEventListener('mousedown', (e) => {
                isDragging = true;
                startX = e.clientX;
                startY = e.clientY;
                initialX = parseInt(element.style.left);
                initialY = parseInt(element.style.top);
                element.style.zIndex = 1000;
            });
            
            document.addEventListener('mousemove', (e) => {
                if (isDragging) {
                    const deltaX = e.clientX - startX;
                    const deltaY = e.clientY - startY;
                    element.style.left = (initialX + deltaX) + 'px';
                    element.style.top = (initialY + deltaY) + 'px';
                    
                    // 更新连线
                    drawEdges();
                }
            });
            
            document.addEventListener('mouseup', () => {
                if (isDragging) {
                    isDragging = false;
                    element.style.zIndex = 'auto';
                }
            });
        }
        
        // 力导向布局
        function applyForceLayout() {
            const iterations = 100;
            const k = 50; // 理想距离
            const c = 0.01; // 阻尼系数
            
            for (let iter = 0; iter < iterations; iter++) {
                // 计算斥力
                for (let i = 0; i < nodes.length; i++) {
                    let fx = 0, fy = 0;
                    
                    for (let j = 0; j < nodes.length; j++) {
                        if (i !== j) {
                            const dx = nodes[i].x - nodes[j].x;
                            const dy = nodes[i].y - nodes[j].y;
                            const distance = Math.sqrt(dx * dx + dy * dy) || 1;
                            const force = k * k / distance;
                            fx += force * dx / distance;
                            fy += force * dy / distance;
                        }
                    }
                    
                    // 计算引力（连接的节点）
                    graphData.edges.forEach(edge => {
                        if (edge.source === nodes[i].data.id) {
                            const target = nodes.find(n => n.data.id === edge.target);
                            if (target) {
                                const dx = target.x - nodes[i].x;
                                const dy = target.y - nodes[i].y;
                                const distance = Math.sqrt(dx * dx + dy * dy) || 1;
                                const force = distance * distance / k;
                                fx += force * dx / distance * 0.1;
                                fy += force * dy / distance * 0.1;
                            }
                        }
                        if (edge.target === nodes[i].data.id) {
                            const source = nodes.find(n => n.data.id === edge.source);
                            if (source) {
                                const dx = source.x - nodes[i].x;
                                const dy = source.y - nodes[i].y;
                                const distance = Math.sqrt(dx * dx + dy * dy) || 1;
                                const force = distance * distance / k;
                                fx += force * dx / distance * 0.1;
                                fy += force * dy / distance * 0.1;
                            }
                        }
                    });
                    
                    // 应用力
                    nodes[i].x += fx * c;
                    nodes[i].y += fy * c;
                    
                    // 边界检查
                    const container = document.getElementById('graphContainer');
                    const containerRect = container.getBoundingClientRect();
                    nodes[i].x = Math.max(0, Math.min(containerRect.width - nodes[i].data.size, nodes[i].x));
                    nodes[i].y = Math.max(0, Math.min(containerRect.height - nodes[i].data.size, nodes[i].y));
                }
            }
            
            // 更新节点位置
            nodes.forEach(node => {
                node.element.style.left = node.x + 'px';
                node.element.style.top = node.y + 'px';
            });
            
            // 重绘连线
            drawEdges();
        }
        
        // 重置布局
        function resetLayout() {
            applyForceLayout();
        }
        
        // 随机布局
        function randomLayout() {
            const container = document.getElementById('graphContainer');
            const containerRect = container.getBoundingClientRect();
            
            nodes.forEach(node => {
                node.x = Math.random() * (containerRect.width - node.data.size);
                node.y = Math.random() * (containerRect.height - node.data.size);
                node.element.style.left = node.x + 'px';
                node.element.style.top = node.y + 'px';
            });
            
            drawEdges();
        }
        
        // 圆形布局
        function circleLayout() {
            const container = document.getElementById('graphContainer');
            const containerRect = container.getBoundingClientRect();
            const centerX = containerRect.width / 2;
            const centerY = containerRect.height / 2;
            const radius = Math.min(centerX, centerY) * 0.7;
            
            nodes.forEach((node, index) => {
                const angle = (index / nodes.length) * 2 * Math.PI;
                node.x = centerX + Math.cos(angle) * radius - node.data.size / 2;
                node.y = centerY + Math.sin(angle) * radius - node.data.size / 2;
                node.element.style.left = node.x + 'px';
                node.element.style.top = node.y + 'px';
            });
            
            drawEdges();
        }
        
        // 导出图片
        function exportImage() {
            const container = document.getElementById('graphContainer');
            
            // 使用html2canvas库（如果可用）或提示用户截图
            alert('请使用浏览器的截图功能或按F12打开开发者工具，在Console中运行以下代码导出图片：\n\nhtml2canvas(document.getElementById("graphContainer")).then(canvas => {\n  const link = document.createElement("a");\n  link.download = "knowledge_graph.png";\n  link.href = canvas.toDataURL();\n  link.click();\n});');
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('正在初始化知识图谱...');
            initGraph();
            console.log('知识图谱初始化完成！');
        });
        
        // 窗口大小改变时重新绘制
        window.addEventListener('resize', () => {
            setTimeout(drawEdges, 100);
        });
    </script>
</body>
</html>
