import os
import shutil
from pathlib import Path

def cleanup_graphrag_files():
    """清理不需要的文件，只保留核心的GraphRAG parquet文件"""
    
    print("🧹 开始清理不需要的文件...")
    
    # 需要保留的核心文件
    keep_files = {
        'entities.parquet',
        'relationships.parquet',
        'communities.parquet'  # 可选保留，用于社区分析
    }
    
    # 可以删除的文件
    optional_delete_files = {
        'text_units.parquet',
        'documents.parquet', 
        'community_reports.parquet'
    }
    
    output_dir = Path('output')
    if not output_dir.exists():
        print("❌ output目录不存在")
        return
    
    print(f"📁 检查 {output_dir} 目录...")
    
    # 统计文件
    total_files = 0
    deleted_files = 0
    kept_files = 0
    
    for file_path in output_dir.iterdir():
        if file_path.is_file():
            total_files += 1
            filename = file_path.name
            
            if filename.endswith('.parquet'):
                if filename in keep_files:
                    print(f"✅ 保留: {filename}")
                    kept_files += 1
                elif filename in optional_delete_files:
                    try:
                        file_path.unlink()
                        print(f"🗑️  删除: {filename}")
                        deleted_files += 1
                    except Exception as e:
                        print(f"❌ 删除失败 {filename}: {e}")
                else:
                    print(f"❓ 未知parquet文件: {filename}")
            else:
                # 非parquet文件，询问是否保留
                if filename.endswith(('.json', '.csv', '.gexf', '.graphml', '.html')):
                    print(f"📄 保留可视化文件: {filename}")
                    kept_files += 1
                else:
                    print(f"📄 其他文件: {filename}")
                    kept_files += 1
    
    print(f"\n📊 清理统计:")
    print(f"  总文件数: {total_files}")
    print(f"  保留文件: {kept_files}")
    print(f"  删除文件: {deleted_files}")
    
    # 显示剩余的核心文件大小
    print(f"\n📋 剩余的核心GraphRAG文件:")
    for filename in keep_files:
        file_path = output_dir / filename
        if file_path.exists():
            size_mb = file_path.stat().st_size / (1024 * 1024)
            print(f"  ✅ {filename}: {size_mb:.2f} MB")
        else:
            print(f"  ❌ {filename}: 不存在")

def show_disk_usage():
    """显示output目录的磁盘使用情况"""
    output_dir = Path('output')
    if not output_dir.exists():
        return
    
    total_size = 0
    file_count = 0
    
    for file_path in output_dir.rglob('*'):
        if file_path.is_file():
            total_size += file_path.stat().st_size
            file_count += 1
    
    size_mb = total_size / (1024 * 1024)
    print(f"\n💾 output目录使用情况:")
    print(f"  文件数量: {file_count}")
    print(f"  总大小: {size_mb:.2f} MB")

if __name__ == "__main__":
    print("GraphRAG 文件清理工具")
    print("=" * 40)
    
    # 显示清理前的状态
    print("🔍 清理前状态:")
    show_disk_usage()
    
    # 执行清理
    cleanup_graphrag_files()
    
    # 显示清理后的状态  
    print("\n🔍 清理后状态:")
    show_disk_usage()
    
    print(f"\n✨ 清理完成！现在只保留核心的GraphRAG文件用于可视化。")
