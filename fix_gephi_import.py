import pandas as pd
import networkx as nx
import xml.etree.ElementTree as ET
from xml.dom import minidom
import csv
from pathlib import Path

def create_gephi_compatible_graph():
    """创建完全兼容Gephi的知识图谱文件"""
    
    print("正在创建Gephi兼容的知识图谱...")
    
    # 读取概念文件
    concepts = []
    with open('concepts.csv', 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        for row in reader:
            if row and row[0].strip():
                concepts.append(row[0].strip())
    
    print(f"处理 {len(concepts)} 个概念")
    
    # 定义简化的概念关系
    relationships = [
        # 核心层次关系
        ('函数', '实参', 'contains', 3.0),
        ('函数', '形参', 'contains', 3.0), 
        ('函数', '返回值', 'contains', 3.0),
        ('实参', '位置实参', 'contains', 2.0),
        ('实参', '关键字实参', 'contains', 2.0),
        ('位置实参', '位置实参的顺序', 'contains', 2.0),
        ('形参', '默认值', 'contains', 2.0),
        
        # 语义关系
        ('实参', '形参', 'corresponds', 2.0),
        ('位置实参', '关键字实参', 'alternative', 1.5),
        ('函数', 'while循环', 'uses', 1.0),
        ('while循环', '列表', 'processes', 1.0),
    ]
    
    # 定义概念类型和属性
    concept_info = {
        '函数': {'type': 'core', 'size': 60, 'color': 'red'},
        '实参': {'type': 'param', 'size': 45, 'color': 'cyan'},
        '形参': {'type': 'param', 'size': 45, 'color': 'cyan'},
        '位置实参': {'type': 'param_type', 'size': 35, 'color': 'blue'},
        '关键字实参': {'type': 'param_type', 'size': 35, 'color': 'blue'},
        '位置实参的顺序': {'type': 'rule', 'size': 25, 'color': 'pink'},
        '默认值': {'type': 'feature', 'size': 30, 'color': 'purple'},
        '返回值': {'type': 'output', 'size': 35, 'color': 'orange'},
        'while循环': {'type': 'control', 'size': 40, 'color': 'green'},
        '列表': {'type': 'data', 'size': 40, 'color': 'yellow'}
    }
    
    # 手动创建GEXF XML
    gexf = ET.Element('gexf')
    gexf.set('xmlns', 'http://www.gexf.net/1.2draft')
    gexf.set('version', '1.2')
    
    # Meta信息
    meta = ET.SubElement(gexf, 'meta')
    meta.set('lastmodifieddate', '2025-06-27')
    creator = ET.SubElement(meta, 'creator')
    creator.text = 'Python Knowledge Graph Generator'
    
    # Graph元素
    graph = ET.SubElement(gexf, 'graph')
    graph.set('mode', 'static')
    graph.set('defaultedgetype', 'undirected')
    
    # 属性定义
    attributes = ET.SubElement(graph, 'attributes')
    attributes.set('class', 'node')
    
    # 节点属性
    attr_type = ET.SubElement(attributes, 'attribute')
    attr_type.set('id', '0')
    attr_type.set('title', 'type')
    attr_type.set('type', 'string')
    
    attr_size = ET.SubElement(attributes, 'attribute')
    attr_size.set('id', '1')
    attr_size.set('title', 'size')
    attr_size.set('type', 'integer')
    
    attr_color = ET.SubElement(attributes, 'attribute')
    attr_color.set('id', '2')
    attr_color.set('title', 'color')
    attr_color.set('type', 'string')
    
    # 边属性
    edge_attributes = ET.SubElement(graph, 'attributes')
    edge_attributes.set('class', 'edge')
    
    edge_attr_type = ET.SubElement(edge_attributes, 'attribute')
    edge_attr_type.set('id', '0')
    edge_attr_type.set('title', 'relationship')
    edge_attr_type.set('type', 'string')
    
    edge_attr_weight = ET.SubElement(edge_attributes, 'attribute')
    edge_attr_weight.set('id', '1')
    edge_attr_weight.set('title', 'weight')
    edge_attr_weight.set('type', 'float')
    
    # 添加节点
    nodes = ET.SubElement(graph, 'nodes')
    
    for concept in concepts:
        if concept in concept_info:
            node = ET.SubElement(nodes, 'node')
            # 使用简单的ID（避免特殊字符）
            node_id = f"node_{concepts.index(concept)}"
            node.set('id', node_id)
            node.set('label', concept)
            
            # 节点属性值
            attvalues = ET.SubElement(node, 'attvalues')
            
            # 类型
            attvalue_type = ET.SubElement(attvalues, 'attvalue')
            attvalue_type.set('for', '0')
            attvalue_type.set('value', concept_info[concept]['type'])
            
            # 大小
            attvalue_size = ET.SubElement(attvalues, 'attvalue')
            attvalue_size.set('for', '1')
            attvalue_size.set('value', str(concept_info[concept]['size']))
            
            # 颜色
            attvalue_color = ET.SubElement(attvalues, 'attvalue')
            attvalue_color.set('for', '2')
            attvalue_color.set('value', concept_info[concept]['color'])
    
    # 添加边
    edges = ET.SubElement(graph, 'edges')
    edge_id = 0
    
    for source, target, rel_type, weight in relationships:
        if source in concepts and target in concepts:
            edge = ET.SubElement(edges, 'edge')
            edge.set('id', str(edge_id))
            edge.set('source', f"node_{concepts.index(source)}")
            edge.set('target', f"node_{concepts.index(target)}")
            
            # 边属性值
            edge_attvalues = ET.SubElement(edge, 'attvalues')
            
            # 关系类型
            edge_attvalue_type = ET.SubElement(edge_attvalues, 'attvalue')
            edge_attvalue_type.set('for', '0')
            edge_attvalue_type.set('value', rel_type)
            
            # 权重
            edge_attvalue_weight = ET.SubElement(edge_attvalues, 'attvalue')
            edge_attvalue_weight.set('for', '1')
            edge_attvalue_weight.set('value', str(weight))
            
            edge_id += 1
    
    # 格式化XML并保存
    rough_string = ET.tostring(gexf, encoding='unicode')
    reparsed = minidom.parseString(rough_string)
    pretty_xml = reparsed.toprettyxml(indent="  ")
    
    # 移除空行
    pretty_xml = '\n'.join([line for line in pretty_xml.split('\n') if line.strip()])
    
    # 保存文件
    output_path = "output/fixed_knowledge_graph.gexf"
    Path("output").mkdir(exist_ok=True)
    
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(pretty_xml)
    
    print(f"修复的图谱文件已保存到: {output_path}")
    print(f"包含 {len(concepts)} 个节点和 {edge_id} 条边")
    
    return output_path

def create_simple_csv_format():
    """同时创建简单的CSV格式供Gephi导入"""
    
    print("创建CSV格式文件...")
    
    # 读取概念
    concepts = []
    with open('concepts.csv', 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        for row in reader:
            if row and row[0].strip():
                concepts.append(row[0].strip())
    
    # 创建节点CSV
    nodes_data = []
    concept_info = {
        '函数': {'type': 'core', 'size': 60, 'color': 'red'},
        '实参': {'type': 'param', 'size': 45, 'color': 'cyan'},
        '形参': {'type': 'param', 'size': 45, 'color': 'cyan'},
        '位置实参': {'type': 'param_type', 'size': 35, 'color': 'blue'},
        '关键字实参': {'type': 'param_type', 'size': 35, 'color': 'blue'},
        '位置实参的顺序': {'type': 'rule', 'size': 25, 'color': 'pink'},
        '默认值': {'type': 'feature', 'size': 30, 'color': 'purple'},
        '返回值': {'type': 'output', 'size': 35, 'color': 'orange'},
        'while循环': {'type': 'control', 'size': 40, 'color': 'green'},
        '列表': {'type': 'data', 'size': 40, 'color': 'yellow'}
    }
    
    for i, concept in enumerate(concepts):
        if concept in concept_info:
            info = concept_info[concept]
            nodes_data.append({
                'Id': f'node_{i}',
                'Label': concept,
                'Type': info['type'],
                'Size': info['size'],
                'Color': info['color']
            })
    
    # 保存节点CSV
    nodes_df = pd.DataFrame(nodes_data)
    nodes_df.to_csv('output/nodes.csv', index=False, encoding='utf-8-sig')
    
    # 创建边CSV
    edges_data = []
    relationships = [
        ('函数', '实参', 'contains', 3.0),
        ('函数', '形参', 'contains', 3.0), 
        ('函数', '返回值', 'contains', 3.0),
        ('实参', '位置实参', 'contains', 2.0),
        ('实参', '关键字实参', 'contains', 2.0),
        ('位置实参', '位置实参的顺序', 'contains', 2.0),
        ('形参', '默认值', 'contains', 2.0),
        ('实参', '形参', 'corresponds', 2.0),
        ('位置实参', '关键字实参', 'alternative', 1.5),
        ('函数', 'while循环', 'uses', 1.0),
        ('while循环', '列表', 'processes', 1.0),
    ]
    
    for i, (source, target, rel_type, weight) in enumerate(relationships):
        if source in concepts and target in concepts:
            edges_data.append({
                'Id': i,
                'Source': f'node_{concepts.index(source)}',
                'Target': f'node_{concepts.index(target)}',
                'Type': 'Undirected',
                'Relationship': rel_type,
                'Weight': weight
            })
    
    # 保存边CSV
    edges_df = pd.DataFrame(edges_data)
    edges_df.to_csv('output/edges.csv', index=False, encoding='utf-8-sig')
    
    print("CSV文件已创建:")
    print("  - output/nodes.csv")
    print("  - output/edges.csv")

if __name__ == "__main__":
    # 创建修复的GEXF文件
    gexf_path = create_gephi_compatible_graph()
    
    # 同时创建CSV格式作为备选
    create_simple_csv_format()
    
    print("\n=== 导入说明 ===")
    print("方案1: 使用修复的GEXF文件")
    print(f"  文件: {gexf_path}")
    print("  操作: 文件 → 打开 → 选择该文件")
    print()
    print("方案2: 使用CSV文件（如果GEXF仍有问题）")
    print("  文件: output/nodes.csv 和 output/edges.csv")
    print("  操作: 数据实验室 → 导入电子表格")
