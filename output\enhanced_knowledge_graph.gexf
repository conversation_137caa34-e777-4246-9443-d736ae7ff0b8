<?xml version="1.0" encoding="UTF-8"?>
ft" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.gexf.net/1.2draft http://www.gexf.net/1.2draft/gexf.xsd" version="1.2">
  <meta lastmodifieddate="2025-06-27">
    <creator>NetworkX 3.4.2</creator>
  </meta>
  <graph defaultedgetype="undirected" mode="static" name="">
    <attributes mode="static" class="edge">
      <attribute id="4" title="relationship" type="string" />
      <attribute id="5" title="description" type="string" />
    </attributes>
    <attributes mode="static" class="node">
      <attribute id="0" title="type" type="string" />
      <attribute id="1" title="description" type="string" />
      <attribute id="2" title="level" type="long" />
      <attribute id="3" title="size" type="long" />
    </attributes>
    <nodes>
      <node id="&#20989;&#25968;" label="&#20989;&#25968;">
        <attvalues>
          <attvalue for="0" value="core_concept" />
          <attvalue for="1" value="Python&#20013;&#30340;&#21487;&#37325;&#29992;&#20195;&#30721;&#22359;&#65292;&#26159;&#31243;&#24207;&#30340;&#22522;&#26412;&#26500;&#24314;&#21333;&#20803;" />
          <attvalue for="2" value="1" />
          <attvalue for="3" value="50" />
        </attvalues>
      </node>
      <node id="&#23454;&#21442;" label="&#23454;&#21442;">
        <attvalues>
          <attvalue for="0" value="parameter_concept" />
          <attvalue for="1" value="&#35843;&#29992;&#20989;&#25968;&#26102;&#20256;&#36882;&#30340;&#23454;&#38469;&#20540;" />
          <attvalue for="2" value="2" />
          <attvalue for="3" value="35" />
        </attvalues>
      </node>
      <node id="&#24418;&#21442;" label="&#24418;&#21442;">
        <attvalues>
          <attvalue for="0" value="parameter_concept" />
          <attvalue for="1" value="&#20989;&#25968;&#23450;&#20041;&#26102;&#30340;&#21442;&#25968;&#21517;&#31216;" />
          <attvalue for="2" value="2" />
          <attvalue for="3" value="35" />
        </attvalues>
      </node>
      <node id="&#20301;&#32622;&#23454;&#21442;" label="&#20301;&#32622;&#23454;&#21442;">
        <attvalues>
          <attvalue for="0" value="parameter_type" />
          <attvalue for="1" value="&#25353;&#29031;&#21442;&#25968;&#20301;&#32622;&#39034;&#24207;&#20256;&#36882;&#30340;&#23454;&#21442;" />
          <attvalue for="2" value="2" />
          <attvalue for="3" value="25" />
        </attvalues>
      </node>
      <node id="&#20301;&#32622;&#23454;&#21442;&#30340;&#39034;&#24207;" label="&#20301;&#32622;&#23454;&#21442;&#30340;&#39034;&#24207;">
        <attvalues>
          <attvalue for="0" value="rule" />
          <attvalue for="1" value="&#20301;&#32622;&#23454;&#21442;&#24517;&#39035;&#25353;&#29031;&#20989;&#25968;&#23450;&#20041;&#30340;&#21442;&#25968;&#39034;&#24207;&#20256;&#36882;" />
          <attvalue for="2" value="3" />
          <attvalue for="3" value="20" />
        </attvalues>
      </node>
      <node id="&#20851;&#38190;&#23383;&#23454;&#21442;" label="&#20851;&#38190;&#23383;&#23454;&#21442;">
        <attvalues>
          <attvalue for="0" value="parameter_type" />
          <attvalue for="1" value="&#20351;&#29992;&#21442;&#25968;&#21517;&#31216;&#20256;&#36882;&#30340;&#23454;&#21442;&#65292;&#19981;&#20381;&#36182;&#20301;&#32622;&#39034;&#24207;" />
          <attvalue for="2" value="3" />
          <attvalue for="3" value="25" />
        </attvalues>
      </node>
      <node id="&#40664;&#35748;&#20540;" label="&#40664;&#35748;&#20540;">
        <attvalues>
          <attvalue for="0" value="feature" />
          <attvalue for="1" value="&#20026;&#24418;&#21442;&#35774;&#32622;&#30340;&#40664;&#35748;&#20540;&#65292;&#35843;&#29992;&#26102;&#21487;&#20197;&#30465;&#30053;&#35813;&#21442;&#25968;" />
          <attvalue for="2" value="3" />
          <attvalue for="3" value="25" />
        </attvalues>
      </node>
      <node id="&#36820;&#22238;&#20540;" label="&#36820;&#22238;&#20540;">
        <attvalues>
          <attvalue for="0" value="output" />
          <attvalue for="1" value="&#20989;&#25968;&#25191;&#34892;&#23436;&#25104;&#21518;&#36820;&#22238;&#32473;&#35843;&#29992;&#32773;&#30340;&#20540;" />
          <attvalue for="2" value="3" />
          <attvalue for="3" value="20" />
        </attvalues>
      </node>
      <node id="while&#24490;&#29615;" label="while&#24490;&#29615;">
        <attvalues>
          <attvalue for="0" value="control_structure" />
          <attvalue for="1" value="&#37325;&#22797;&#25191;&#34892;&#20195;&#30721;&#22359;&#30452;&#21040;&#26465;&#20214;&#20026;&#20551;&#30340;&#24490;&#29615;&#32467;&#26500;" />
          <attvalue for="2" value="1" />
          <attvalue for="3" value="35" />
        </attvalues>
      </node>
      <node id="&#21015;&#34920;" label="&#21015;&#34920;">
        <attvalues>
          <attvalue for="0" value="data_structure" />
          <attvalue for="1" value="Python&#20013;&#23384;&#20648;&#22810;&#20010;&#26377;&#24207;&#20803;&#32032;&#30340;&#21487;&#21464;&#25968;&#25454;&#31867;&#22411;" />
          <attvalue for="2" value="1" />
          <attvalue for="3" value="35" />
        </attvalues>
      </node>
    </nodes>
    <edges>
      <edge source="&#20989;&#25968;" target="&#23454;&#21442;" id="0" weight="3.0">
        <attvalues>
          <attvalue for="4" value="contains" />
          <attvalue for="5" value="&#20989;&#25968;&#21253;&#21547;&#23454;&#21442;" />
        </attvalues>
      </edge>
      <edge source="&#20989;&#25968;" target="&#24418;&#21442;" id="1" weight="3.0">
        <attvalues>
          <attvalue for="4" value="contains" />
          <attvalue for="5" value="&#20989;&#25968;&#21253;&#21547;&#24418;&#21442;" />
        </attvalues>
      </edge>
      <edge source="&#20989;&#25968;" target="&#36820;&#22238;&#20540;" id="2" weight="3.0">
        <attvalues>
          <attvalue for="4" value="contains" />
          <attvalue for="5" value="&#20989;&#25968;&#21253;&#21547;&#36820;&#22238;&#20540;" />
        </attvalues>
      </edge>
      <edge source="&#20989;&#25968;" target="while&#24490;&#29615;" id="3" weight="1.0">
        <attvalues>
          <attvalue for="4" value="used_with" />
          <attvalue for="5" value="&#20989;&#25968;&#20013;&#21487;&#20197;&#20351;&#29992;while&#24490;&#29615;" />
        </attvalues>
      </edge>
      <edge source="&#23454;&#21442;" target="&#20301;&#32622;&#23454;&#21442;" id="4" weight="3.0">
        <attvalues>
          <attvalue for="4" value="contains" />
          <attvalue for="5" value="&#23454;&#21442;&#21253;&#21547;&#20301;&#32622;&#23454;&#21442;" />
        </attvalues>
      </edge>
      <edge source="&#23454;&#21442;" target="&#20851;&#38190;&#23383;&#23454;&#21442;" id="5" weight="3.0">
        <attvalues>
          <attvalue for="4" value="contains" />
          <attvalue for="5" value="&#23454;&#21442;&#21253;&#21547;&#20851;&#38190;&#23383;&#23454;&#21442;" />
        </attvalues>
      </edge>
      <edge source="&#23454;&#21442;" target="&#24418;&#21442;" id="6" weight="2.0">
        <attvalues>
          <attvalue for="4" value="corresponds_to" />
          <attvalue for="5" value="&#23454;&#21442;&#23545;&#24212;&#24418;&#21442;" />
        </attvalues>
      </edge>
      <edge source="&#24418;&#21442;" target="&#40664;&#35748;&#20540;" id="7" weight="3.0">
        <attvalues>
          <attvalue for="4" value="contains" />
          <attvalue for="5" value="&#24418;&#21442;&#21253;&#21547;&#40664;&#35748;&#20540;" />
        </attvalues>
      </edge>
      <edge source="&#20301;&#32622;&#23454;&#21442;" target="&#20301;&#32622;&#23454;&#21442;&#30340;&#39034;&#24207;" id="8" weight="3.0">
        <attvalues>
          <attvalue for="4" value="contains" />
          <attvalue for="5" value="&#20301;&#32622;&#23454;&#21442;&#21253;&#21547;&#20301;&#32622;&#23454;&#21442;&#30340;&#39034;&#24207;" />
        </attvalues>
      </edge>
      <edge source="&#20301;&#32622;&#23454;&#21442;" target="&#20851;&#38190;&#23383;&#23454;&#21442;" id="9" weight="1.5">
        <attvalues>
          <attvalue for="4" value="alternative_to" />
          <attvalue for="5" value="&#20301;&#32622;&#23454;&#21442;&#21644;&#20851;&#38190;&#23383;&#23454;&#21442;&#26159;&#20004;&#31181;&#20256;&#21442;&#26041;&#24335;" />
        </attvalues>
      </edge>
      <edge source="&#36820;&#22238;&#20540;" target="&#21015;&#34920;" id="10" weight="1.0">
        <attvalues>
          <attvalue for="4" value="can_be" />
          <attvalue for="5" value="&#36820;&#22238;&#20540;&#21487;&#20197;&#26159;&#21015;&#34920;" />
        </attvalues>
      </edge>
      <edge source="while&#24490;&#29615;" target="&#21015;&#34920;" id="11" weight="1.0">
        <attvalues>
          <attvalue for="4" value="processes" />
          <attvalue for="5" value="while&#24490;&#29615;&#21487;&#20197;&#22788;&#29702;&#21015;&#34920;" />
        </attvalues>
      </edge>
    </edges>
  </graph>
</gexf>