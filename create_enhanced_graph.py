import pandas as pd
import networkx as nx
import csv
from pathlib import Path

def create_enhanced_knowledge_graph():
    """基于concepts.csv创建增强的知识图谱"""
    
    print("正在读取concepts.csv文件...")
    
    # 读取概念文件
    concepts = []
    with open('concepts.csv', 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        for row in reader:
            if row and row[0].strip():  # 跳过空行
                concepts.append(row[0].strip())
    
    print(f"找到 {len(concepts)} 个核心概念")
    
    # 定义概念之间的关系和层次结构
    concept_relationships = {
        # 函数相关概念
        '函数': {
            'children': ['实参', '形参', '返回值'],
            'type': 'core_concept',
            'description': 'Python中的可重用代码块，是程序的基本构建单元'
        },
        '实参': {
            'children': ['位置实参', '关键字实参'],
            'parent': '函数',
            'type': 'parameter_concept',
            'description': '调用函数时传递的实际值'
        },
        '形参': {
            'children': ['默认值'],
            'parent': '函数',
            'type': 'parameter_concept', 
            'description': '函数定义时的参数名称'
        },
        '位置实参': {
            'children': ['位置实参的顺序'],
            'parent': '实参',
            'type': 'parameter_type',
            'description': '按照参数位置顺序传递的实参'
        },
        '位置实参的顺序': {
            'parent': '位置实参',
            'type': 'rule',
            'description': '位置实参必须按照函数定义的参数顺序传递'
        },
        '关键字实参': {
            'parent': '实参',
            'type': 'parameter_type',
            'description': '使用参数名称传递的实参，不依赖位置顺序'
        },
        '默认值': {
            'parent': '形参',
            'type': 'feature',
            'description': '为形参设置的默认值，调用时可以省略该参数'
        },
        '返回值': {
            'parent': '函数',
            'type': 'output',
            'description': '函数执行完成后返回给调用者的值'
        },
        # 控制结构
        'while循环': {
            'type': 'control_structure',
            'description': '重复执行代码块直到条件为假的循环结构'
        },
        # 数据结构
        '列表': {
            'type': 'data_structure',
            'description': 'Python中存储多个有序元素的可变数据类型'
        }
    }
    
    # 创建NetworkX图
    G = nx.DiGraph()  # 使用有向图来表示层次关系
    
    # 添加节点
    for concept in concepts:
        if concept in concept_relationships:
            info = concept_relationships[concept]
            G.add_node(
                concept,
                label=concept,
                type=info.get('type', 'concept'),
                description=info.get('description', ''),
                level=get_concept_level(concept, concept_relationships),
                size=get_node_size(concept, concept_relationships)
            )
    
    # 添加层次关系边
    for concept, info in concept_relationships.items():
        if concept in concepts:
            # 添加父子关系
            if 'children' in info:
                for child in info['children']:
                    if child in concepts:
                        G.add_edge(concept, child, 
                                 relationship='contains',
                                 weight=3.0,
                                 description=f'{concept}包含{child}')
            
            # 添加相关概念关系
            if 'parent' in info:
                parent = info['parent']
                if parent in concepts:
                    G.add_edge(parent, concept,
                             relationship='contains', 
                             weight=3.0,
                             description=f'{parent}包含{concept}')
    
    # 添加概念间的语义关系
    semantic_relations = [
        ('实参', '形参', 'corresponds_to', 2.0, '实参对应形参'),
        ('位置实参', '关键字实参', 'alternative_to', 1.5, '位置实参和关键字实参是两种传参方式'),
        ('函数', 'while循环', 'used_with', 1.0, '函数中可以使用while循环'),
        ('while循环', '列表', 'processes', 1.0, 'while循环可以处理列表'),
        ('返回值', '列表', 'can_be', 1.0, '返回值可以是列表')
    ]
    
    for source, target, rel_type, weight, desc in semantic_relations:
        if source in concepts and target in concepts:
            G.add_edge(source, target,
                     relationship=rel_type,
                     weight=weight,
                     description=desc)
    
    return G, concept_relationships

def get_concept_level(concept, relationships):
    """计算概念的层次级别"""
    if 'parent' not in relationships.get(concept, {}):
        return 1  # 顶级概念
    elif 'children' in relationships.get(concept, {}):
        return 2  # 中级概念
    else:
        return 3  # 叶子概念

def get_node_size(concept, relationships):
    """根据概念重要性计算节点大小"""
    info = relationships.get(concept, {})
    if info.get('type') == 'core_concept':
        return 50
    elif info.get('type') in ['parameter_concept', 'control_structure', 'data_structure']:
        return 35
    elif info.get('type') in ['parameter_type', 'feature']:
        return 25
    else:
        return 20

def export_enhanced_graph():
    """导出增强的知识图谱"""
    
    G, relationships = create_enhanced_knowledge_graph()
    
    print(f"生成的图谱包含:")
    print(f"  - 节点数: {G.number_of_nodes()}")
    print(f"  - 边数: {G.number_of_edges()}")
    
    # 导出为GEXF格式
    gexf_path = "output/enhanced_knowledge_graph.gexf"
    
    # 确保输出目录存在
    Path("output").mkdir(exist_ok=True)
    
    # 转换为无向图用于Gephi显示
    G_undirected = G.to_undirected()
    
    with open(gexf_path, 'w', encoding='utf-8') as f:
        f.write('<?xml version="1.0" encoding="UTF-8"?>\n')
        gexf_content = '\n'.join(nx.generate_gexf(G_undirected))[39:]
        f.write(gexf_content)
    
    # 也导出GraphML格式
    graphml_path = "output/enhanced_knowledge_graph.graphml"
    nx.write_graphml(G_undirected, graphml_path, encoding='utf-8')
    
    print(f"\n增强知识图谱已导出到:")
    print(f"  - GEXF格式: {gexf_path}")
    print(f"  - GraphML格式: {graphml_path}")
    
    # 显示概念分类统计
    types = {}
    for node, data in G.nodes(data=True):
        node_type = data.get('type', 'unknown')
        types[node_type] = types.get(node_type, 0) + 1
    
    print(f"\n概念分类统计:")
    for type_name, count in types.items():
        print(f"  - {type_name}: {count}个")
    
    # 显示关系类型统计
    rel_types = {}
    for source, target, data in G.edges(data=True):
        rel_type = data.get('relationship', 'unknown')
        rel_types[rel_type] = rel_types.get(rel_type, 0) + 1
    
    print(f"\n关系类型统计:")
    for rel_type, count in rel_types.items():
        print(f"  - {rel_type}: {count}个")
    
    return gexf_path, graphml_path

if __name__ == "__main__":
    export_enhanced_graph()
