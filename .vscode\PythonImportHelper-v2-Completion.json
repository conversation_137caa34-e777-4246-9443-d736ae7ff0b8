[{"label": "pandas", "kind": 6, "isExtraImport": true, "importPath": "pandas", "description": "pandas", "detail": "pandas", "documentation": {}}, {"label": "os", "kind": 6, "isExtraImport": true, "importPath": "os", "description": "os", "detail": "os", "documentation": {}}, {"label": "shutil", "kind": 6, "isExtraImport": true, "importPath": "shutil", "description": "shutil", "detail": "shutil", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "networkx", "kind": 6, "isExtraImport": true, "importPath": "networkx", "description": "networkx", "detail": "networkx", "documentation": {}}, {"label": "csv", "kind": 6, "isExtraImport": true, "importPath": "csv", "description": "csv", "detail": "csv", "documentation": {}}, {"label": "json", "kind": 6, "isExtraImport": true, "importPath": "json", "description": "json", "detail": "json", "documentation": {}}, {"label": "xml.etree.ElementTree", "kind": 6, "isExtraImport": true, "importPath": "xml.etree.ElementTree", "description": "xml.etree.ElementTree", "detail": "xml.etree.ElementTree", "documentation": {}}, {"label": "minidom", "importPath": "xml.dom", "description": "xml.dom", "isExtraImport": true, "detail": "xml.dom", "documentation": {}}, {"label": "analyze_graphrag_parquet", "kind": 2, "importPath": "analyze_parquet", "description": "analyze_parquet", "peekOfCode": "def analyze_graphrag_parquet():\n    \"\"\"分析 GraphRAG 生成的 parquet 文件\"\"\"\n    print(\"GraphRAG Parquet 文件分析:\")\n    print(\"=\" * 50)\n    # 检查主要的 parquet 文件\n    files_to_check = [\n        'entities.parquet',\n        'relationships.parquet', \n        'communities.parquet',\n        'text_units.parquet',", "detail": "analyze_parquet", "documentation": {}}, {"label": "df", "kind": 5, "importPath": "check_entities", "description": "check_entities", "peekOfCode": "df = pd.read_parquet('output/entities.parquet')\nprint('总实体数量:', len(df))\nprint('\\n您的10个概念在实体中的情况:')\nconcepts = ['函数', '实参', '形参', '位置实参', '位置实参的顺序', '关键字实参', '默认值', '返回值', 'while循环', '列表']\nfor concept in concepts:\n    matches = df[df['title'] == concept]\n    print(f'{concept}: {len(matches)} 个实体')\nprint('\\n所有实体类型统计:')\nprint(df['type'].value_counts())\nprint('\\n包含您概念的实体详情:')", "detail": "check_entities", "documentation": {}}, {"label": "concepts", "kind": 5, "importPath": "check_entities", "description": "check_entities", "peekOfCode": "concepts = ['函数', '实参', '形参', '位置实参', '位置实参的顺序', '关键字实参', '默认值', '返回值', 'while循环', '列表']\nfor concept in concepts:\n    matches = df[df['title'] == concept]\n    print(f'{concept}: {len(matches)} 个实体')\nprint('\\n所有实体类型统计:')\nprint(df['type'].value_counts())\nprint('\\n包含您概念的实体详情:')\nfor concept in concepts:\n    matches = df[df['title'] == concept]\n    if len(matches) > 0:", "detail": "check_entities", "documentation": {}}, {"label": "cleanup_graphrag_files", "kind": 2, "importPath": "cleanup_files", "description": "cleanup_files", "peekOfCode": "def cleanup_graphrag_files():\n    \"\"\"清理不需要的文件，只保留核心的GraphRAG parquet文件\"\"\"\n    print(\"🧹 开始清理不需要的文件...\")\n    # 需要保留的核心文件\n    keep_files = {\n        'entities.parquet',\n        'relationships.parquet',\n        'communities.parquet'  # 可选保留，用于社区分析\n    }\n    # 可以删除的文件", "detail": "cleanup_files", "documentation": {}}, {"label": "show_disk_usage", "kind": 2, "importPath": "cleanup_files", "description": "cleanup_files", "peekOfCode": "def show_disk_usage():\n    \"\"\"显示output目录的磁盘使用情况\"\"\"\n    output_dir = Path('output')\n    if not output_dir.exists():\n        return\n    total_size = 0\n    file_count = 0\n    for file_path in output_dir.rglob('*'):\n        if file_path.is_file():\n            total_size += file_path.stat().st_size", "detail": "cleanup_files", "documentation": {}}, {"label": "create_enhanced_knowledge_graph", "kind": 2, "importPath": "create_enhanced_graph", "description": "create_enhanced_graph", "peekOfCode": "def create_enhanced_knowledge_graph():\n    \"\"\"基于concepts.csv创建增强的知识图谱\"\"\"\n    print(\"正在读取concepts.csv文件...\")\n    # 读取概念文件\n    concepts = []\n    with open('concepts.csv', 'r', encoding='utf-8') as f:\n        reader = csv.reader(f)\n        for row in reader:\n            if row and row[0].strip():  # 跳过空行\n                concepts.append(row[0].strip())", "detail": "create_enhanced_graph", "documentation": {}}, {"label": "get_concept_level", "kind": 2, "importPath": "create_enhanced_graph", "description": "create_enhanced_graph", "peekOfCode": "def get_concept_level(concept, relationships):\n    \"\"\"计算概念的层次级别\"\"\"\n    if 'parent' not in relationships.get(concept, {}):\n        return 1  # 顶级概念\n    elif 'children' in relationships.get(concept, {}):\n        return 2  # 中级概念\n    else:\n        return 3  # 叶子概念\ndef get_node_size(concept, relationships):\n    \"\"\"根据概念重要性计算节点大小\"\"\"", "detail": "create_enhanced_graph", "documentation": {}}, {"label": "get_node_size", "kind": 2, "importPath": "create_enhanced_graph", "description": "create_enhanced_graph", "peekOfCode": "def get_node_size(concept, relationships):\n    \"\"\"根据概念重要性计算节点大小\"\"\"\n    info = relationships.get(concept, {})\n    if info.get('type') == 'core_concept':\n        return 50\n    elif info.get('type') in ['parameter_concept', 'control_structure', 'data_structure']:\n        return 35\n    elif info.get('type') in ['parameter_type', 'feature']:\n        return 25\n    else:", "detail": "create_enhanced_graph", "documentation": {}}, {"label": "export_enhanced_graph", "kind": 2, "importPath": "create_enhanced_graph", "description": "create_enhanced_graph", "peekOfCode": "def export_enhanced_graph():\n    \"\"\"导出增强的知识图谱\"\"\"\n    G, relationships = create_enhanced_knowledge_graph()\n    print(f\"生成的图谱包含:\")\n    print(f\"  - 节点数: {G.number_of_nodes()}\")\n    print(f\"  - 边数: {G.number_of_edges()}\")\n    # 导出为GEXF格式\n    gexf_path = \"output/enhanced_knowledge_graph.gexf\"\n    # 确保输出目录存在\n    Path(\"output\").mkdir(exist_ok=True)", "detail": "create_enhanced_graph", "documentation": {}}, {"label": "create_gephi_optimized_graph", "kind": 2, "importPath": "create_optimized_graph", "description": "create_optimized_graph", "peekOfCode": "def create_gephi_optimized_graph():\n    \"\"\"创建专门为Gephi优化的知识图谱\"\"\"\n    # 读取之前生成的图谱\n    G = nx.read_graphml(\"output/enhanced_knowledge_graph.graphml\")\n    # 为Gephi优化节点属性\n    node_colors = {\n        'core_concept': '#FF6B6B',      # 红色 - 核心概念\n        'parameter_concept': '#4ECDC4', # 青色 - 参数概念  \n        'parameter_type': '#45B7D1',    # 蓝色 - 参数类型\n        'control_structure': '#96CEB4', # 绿色 - 控制结构", "detail": "create_optimized_graph", "documentation": {}}, {"label": "export_to_gephi", "kind": 2, "importPath": "export_to_gephi", "description": "export_to_gephi", "peekOfCode": "def export_to_gephi(output_dir=\"output\"):\n    \"\"\"将GraphRAG输出转换为Gephi可读格式\"\"\"\n    print(\"正在读取GraphRAG输出数据...\")\n    # 读取实体和关系数据\n    entities_df = pd.read_parquet(f\"{output_dir}/entities.parquet\")\n    relationships_df = pd.read_parquet(f\"{output_dir}/relationships.parquet\")\n    print(f\"找到 {len(entities_df)} 个实体和 {len(relationships_df)} 个关系\")\n    # 创建NetworkX图\n    G = nx.Graph()\n    # 添加节点", "detail": "export_to_gephi", "documentation": {}}, {"label": "export_to_gephi_utf8", "kind": 2, "importPath": "export_to_gephi_utf8", "description": "export_to_gephi_utf8", "peekOfCode": "def export_to_gephi_utf8(output_dir=\"output\"):\n    \"\"\"将GraphRAG输出转换为UTF-8编码的Gephi格式\"\"\"\n    print(\"正在读取GraphRAG输出数据...\")\n    # 读取实体和关系数据\n    entities_df = pd.read_parquet(f\"{output_dir}/entities.parquet\")\n    relationships_df = pd.read_parquet(f\"{output_dir}/relationships.parquet\")\n    print(f\"找到 {len(entities_df)} 个实体和 {len(relationships_df)} 个关系\")\n    # 创建NetworkX图\n    G = nx.Graph()\n    # 添加节点，确保中文字符正确处理", "detail": "export_to_gephi_utf8", "documentation": {}}, {"label": "create_gephi_compatible_graph", "kind": 2, "importPath": "fix_gephi_import", "description": "fix_gephi_import", "peekOfCode": "def create_gephi_compatible_graph():\n    \"\"\"创建完全兼容Gephi的知识图谱文件\"\"\"\n    print(\"正在创建Gephi兼容的知识图谱...\")\n    # 读取概念文件\n    concepts = []\n    with open('concepts.csv', 'r', encoding='utf-8') as f:\n        reader = csv.reader(f)\n        for row in reader:\n            if row and row[0].strip():\n                concepts.append(row[0].strip())", "detail": "fix_gephi_import", "documentation": {}}, {"label": "create_simple_csv_format", "kind": 2, "importPath": "fix_gephi_import", "description": "fix_gephi_import", "peekOfCode": "def create_simple_csv_format():\n    \"\"\"同时创建简单的CSV格式供Gephi导入\"\"\"\n    print(\"创建CSV格式文件...\")\n    # 读取概念\n    concepts = []\n    with open('concepts.csv', 'r', encoding='utf-8') as f:\n        reader = csv.reader(f)\n        for row in reader:\n            if row and row[0].strip():\n                concepts.append(row[0].strip())", "detail": "fix_gephi_import", "documentation": {}}, {"label": "test_parquet_relationships", "kind": 2, "importPath": "test_relationships", "description": "test_relationships", "peekOfCode": "def test_parquet_relationships():\n    \"\"\"测试当前parquet数据中节点与节点之间的关系\"\"\"\n    print(\"🔍 测试 GraphRAG Parquet 数据中的节点关系\")\n    print(\"=\" * 60)\n    # 读取数据\n    entities_df = pd.read_parquet('output/entities.parquet')\n    relationships_df = pd.read_parquet('output/relationships.parquet')\n    print(f\"📊 数据概览:\")\n    print(f\"  实体数量: {len(entities_df)}\")\n    print(f\"  关系数量: {len(relationships_df)}\")", "detail": "test_relationships", "documentation": {}}, {"label": "create_updated_visualization", "kind": 2, "importPath": "update_visualization", "description": "update_visualization", "peekOfCode": "def create_updated_visualization():\n    \"\"\"基于GraphRAG实际输出创建更新的可视化文件\"\"\"\n    print(\"正在基于GraphRAG输出创建更新的可视化...\")\n    # 读取GraphRAG生成的实体数据\n    entities_df = pd.read_parquet('output/entities.parquet')\n    relationships_df = pd.read_parquet('output/relationships.parquet')\n    # 您的10个概念\n    target_concepts = ['函数', '实参', '形参', '位置实参', '位置实参的顺序', '关键字实参', '默认值', '返回值', 'while循环', '列表']\n    # 查找所有相关实体（包括大小写变体）\n    concept_entities = {}", "detail": "update_visualization", "documentation": {}}]