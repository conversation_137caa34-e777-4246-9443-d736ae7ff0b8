import pandas as pd
import networkx as nx
from pathlib import Path

def export_to_gephi(output_dir="output"):
    """将GraphRAG输出转换为Gephi可读格式"""
    
    print("正在读取GraphRAG输出数据...")
    
    # 读取实体和关系数据
    entities_df = pd.read_parquet(f"{output_dir}/entities.parquet")
    relationships_df = pd.read_parquet(f"{output_dir}/relationships.parquet")
    
    print(f"找到 {len(entities_df)} 个实体和 {len(relationships_df)} 个关系")
    
    # 创建NetworkX图
    G = nx.Graph()
    
    # 添加节点
    for _, entity in entities_df.iterrows():
        G.add_node(
            entity['title'],
            label=entity['title'],
            type=entity.get('type', 'entity'),
            description=entity.get('description', ''),
            degree=entity.get('degree', 0),
            community_ids=str(entity.get('community_ids', []))
        )
    
    # 添加边
    for _, rel in relationships_df.iterrows():
        if rel['source'] in G.nodes() and rel['target'] in G.nodes():
            G.add_edge(
                rel['source'],
                rel['target'],
                weight=rel.get('weight', 1.0),
                description=rel.get('description', ''),
                rank=rel.get('rank', 0)
            )
    
    # 导出为GEXF格式（Gephi原生格式）
    gexf_path = f"{output_dir}/knowledge_graph.gexf"
    nx.write_gexf(G, gexf_path)
    
    # 也可以导出为GraphML格式
    graphml_path = f"{output_dir}/knowledge_graph.graphml"
    nx.write_graphml(G, graphml_path)
    
    print(f"图谱已导出到:")
    print(f"  - GEXF格式: {gexf_path}")
    print(f"  - GraphML格式: {graphml_path}")
    print(f"节点数: {G.number_of_nodes()}")
    print(f"边数: {G.number_of_edges()}")
    
    return gexf_path, graphml_path

if __name__ == "__main__":
    export_to_gephi()
