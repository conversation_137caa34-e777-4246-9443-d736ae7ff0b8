import pandas as pd
import json
from pathlib import Path

def create_updated_visualization():
    """基于GraphRAG实际输出创建更新的可视化文件"""
    
    print("正在基于GraphRAG输出创建更新的可视化...")
    
    # 读取GraphRAG生成的实体数据
    entities_df = pd.read_parquet('output/entities.parquet')
    relationships_df = pd.read_parquet('output/relationships.parquet')
    
    # 您的10个概念
    target_concepts = ['函数', '实参', '形参', '位置实参', '位置实参的顺序', '关键字实参', '默认值', '返回值', 'while循环', '列表']
    
    # 查找所有相关实体（包括大小写变体）
    concept_entities = {}
    for concept in target_concepts:
        # 精确匹配
        exact_matches = entities_df[entities_df['title'] == concept]
        if len(exact_matches) > 0:
            concept_entities[concept] = exact_matches.iloc[0]
        else:
            # 尝试大小写变体
            upper_matches = entities_df[entities_df['title'] == concept.upper()]
            if len(upper_matches) > 0:
                concept_entities[concept] = upper_matches.iloc[0]
                print(f"找到大小写变体: {concept} -> {concept.upper()}")
            else:
                # 尝试包含匹配
                contains_matches = entities_df[entities_df['title'].str.contains(concept, na=False)]
                if len(contains_matches) > 0:
                    concept_entities[concept] = contains_matches.iloc[0]
                    print(f"找到包含匹配: {concept} -> {contains_matches.iloc[0]['title']}")
                else:
                    print(f"警告: 未找到概念 '{concept}' 的匹配实体")
    
    print(f"成功匹配 {len(concept_entities)} 个概念")
    
    # 创建节点数据
    nodes = []
    node_colors = {
        '函数': '#e74c3c',      # 红色 - 核心概念
        '实参': '#3498db',      # 蓝色 - 参数类
        '形参': '#3498db',      # 蓝色 - 参数类
        '位置实参': '#2ecc71',  # 绿色 - 参数子类
        '位置实参的顺序': '#27ae60', # 深绿色 - 规则
        '关键字实参': '#2ecc71', # 绿色 - 参数子类
        '默认值': '#9b59b6',    # 紫色 - 特性
        '返回值': '#f39c12',    # 橙色 - 输出
        'while循环': '#1abc9c', # 青色 - 控制结构
        '列表': '#f1c40f'       # 黄色 - 数据结构
    }
    
    node_sizes = {
        '函数': 60,
        '实参': 45,
        '形参': 45,
        '位置实参': 35,
        '位置实参的顺序': 25,
        '关键字实参': 35,
        '默认值': 30,
        '返回值': 40,
        'while循环': 40,
        '列表': 40
    }
    
    for concept in target_concepts:
        if concept in concept_entities:
            entity = concept_entities[concept]
            nodes.append({
                "id": concept,
                "label": concept,
                "type": entity.get('type', 'concept'),
                "size": node_sizes.get(concept, 35),
                "color": node_colors.get(concept, '#95a5a6'),
                "description": entity.get('description', '')[:200] + '...' if len(str(entity.get('description', ''))) > 200 else entity.get('description', '')
            })
        else:
            # 如果没有找到实体，创建一个默认节点
            nodes.append({
                "id": concept,
                "label": concept,
                "type": "concept",
                "size": node_sizes.get(concept, 35),
                "color": node_colors.get(concept, '#95a5a6'),
                "description": f"概念: {concept}"
            })
    
    # 创建边数据（基于概念间的逻辑关系）
    edges = []
    concept_relationships = [
        ('函数', '实参', 'contains', 3.0),
        ('函数', '形参', 'contains', 3.0),
        ('函数', '返回值', 'contains', 3.0),
        ('实参', '位置实参', 'contains', 2.0),
        ('实参', '关键字实参', 'contains', 2.0),
        ('位置实参', '位置实参的顺序', 'contains', 2.0),
        ('形参', '默认值', 'contains', 2.0),
        ('实参', '形参', 'corresponds', 2.0),
        ('位置实参', '关键字实参', 'alternative', 1.5),
        ('函数', 'while循环', 'uses', 1.0),
        ('while循环', '列表', 'processes', 1.0),
    ]
    
    for i, (source, target, rel_type, weight) in enumerate(concept_relationships):
        if source in [node['id'] for node in nodes] and target in [node['id'] for node in nodes]:
            edges.append({
                "id": f"edge_{i}",
                "source": source,
                "target": target,
                "type": rel_type,
                "weight": weight,
                "description": f"{source} {rel_type} {target}"
            })
    
    # 创建Cytoscape.js格式
    cytoscape_data = {
        "elements": {
            "nodes": [{"data": node} for node in nodes],
            "edges": [{"data": edge} for edge in edges]
        }
    }
    
    # 保存更新的可视化文件
    output_path = "output/cytoscape_graph.json"
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(cytoscape_data, f, ensure_ascii=False, indent=2)
    
    # 同时创建CSV格式的节点和边文件
    nodes_df = pd.DataFrame([{
        'Id': f'node_{i}',
        'Label': node['label'],
        'Type': node['type'],
        'Size': node['size'],
        'Color': node['color']
    } for i, node in enumerate(nodes)])
    
    edges_df = pd.DataFrame([{
        'Id': edge['id'],
        'Source': f'node_{[n["label"] for n in nodes].index(edge["source"])}',
        'Target': f'node_{[n["label"] for n in nodes].index(edge["target"])}',
        'Type': 'Undirected',
        'Relationship': edge['type'],
        'Weight': edge['weight']
    } for edge in edges])
    
    nodes_df.to_csv('output/nodes.csv', index=False, encoding='utf-8-sig')
    edges_df.to_csv('output/edges.csv', index=False, encoding='utf-8-sig')
    
    print(f"更新的可视化文件已保存:")
    print(f"  - Cytoscape.js: {output_path}")
    print(f"  - 节点CSV: output/nodes.csv")
    print(f"  - 边CSV: output/edges.csv")
    print(f"节点数: {len(nodes)}, 边数: {len(edges)}")
    
    return output_path

if __name__ == "__main__":
    create_updated_visualization()
