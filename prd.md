# GraphRAG 知识图谱构建与 Gephi 可视化 PRD

## 1. 项目概述

### 1.1 项目目标

使用 Microsoft GraphRAG 框架从文档中构建知识图谱，并通过 Gephi 进行可视化分析。

### 1.2 核心价值

- 从非结构化文本中提取结构化知识
- 构建实体关系网络
- 提供直观的图谱可视化
- 支持复杂查询和分析

## 2. 技术架构

### 2.1 核心组件

- **GraphRAG**: Microsoft 开源的知识图谱构建框架
- **Gephi**: 开源网络分析和可视化软件
- **LLM**: 大语言模型（通过 OpenRouter API 访问）
- **Python 环境**: 运行 GraphRAG 的基础环境

### 2.2 数据流程

```
原始文档 → GraphRAG处理 → 知识图谱 → 导出格式 → Gephi可视化
```

## 3. 实施步骤

### 3.1 环境准备

#### 3.1.1 安装 GraphRAG

```bash
# 创建虚拟环境（已完成）
python -m venv .venv
.venv\Scripts\activate  # Windows 激活虚拟环境

# 安装GraphRAG（已完成）
pip install graphrag
```

#### 3.1.2 安装 Gephi

- 下载地址: https://gephi.org/
- 支持 Windows、macOS、Linux
- 需要 Java 8+环境

### 3.2 项目初始化

#### 3.2.1 创建项目目录

```bash
# 项目目录已存在：C:\Users\<USER>\Desktop\graphrag
# 当前工作目录即为项目根目录
```

#### 3.2.2 初始化 GraphRAG

```bash
python -m graphrag.index --init --root .
```

#### 3.2.3 目录结构

```
C:\Users\<USER>\Desktop\graphrag/
├── .venv/                  # 虚拟环境（已创建）
├── .env                    # 环境变量配置
├── settings.yaml          # GraphRAG配置文件
├── input/                  # 输入文档目录
│   └── 第八章.md           # 输入文档
├── output/                 # 输出结果目录
└── 第八章.md               # 原始文档文件
```

### 3.3 配置设置

#### 3.3.1 OpenRouter API 说明

本项目使用 OpenRouter API 来访问 GPT-4.1 模型，OpenRouter 提供了统一的 API 接口来访问多种大语言模型：

- **优势**: 支持多种模型、价格透明、无需多个 API 密钥
- **模型**: 使用 `openai/gpt-4.1` 获得最佳的实体抽取和关系识别效果
- **API 兼容**: 完全兼容 OpenAI API 格式

#### 3.3.2 环境变量配置 (.env)

```env
# OpenRouter API配置
GRAPHRAG_API_KEY=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
GRAPHRAG_API_BASE=https://openrouter.ai/api/v1

```

#### 3.3.3 基础配置 (settings.yaml)

```yaml
llm:
  api_key: ${GRAPHRAG_API_KEY}
  api_base: ${GRAPHRAG_API_BASE}
  type: openai_chat
  model: openai/gpt-4.1
  max_tokens: 4000
  temperature: 0

embeddings:
  llm:
    api_key: ${GRAPHRAG_API_KEY}
    api_base: ${GRAPHRAG_API_BASE}
    type: openai_embedding
    model: text-embedding-3-small

input:
  type: file
  file_type: text
  base_dir: "input"
  file_encoding: utf-8

storage:
  type: file
  base_dir: "output"

cache:
  type: file
  base_dir: "cache"

reporting:
  type: file
  base_dir: "reporting"
```

### 3.4 数据准备

#### 3.4.1 准备输入文档

- 将待分析的文档放入 `input/` 目录
- 支持格式：.txt, .md, .docx, .pdf
- 建议单个文件不超过 10MB

#### 3.4.2 文档预处理建议

- 确保文档编码为 UTF-8
- 移除不必要的格式标记
- 保持文本结构清晰

### 3.5 构建知识图谱

#### 3.5.1 运行索引构建

```bash
python -m graphrag.index --root .
```

#### 3.5.2 监控处理进度

- 查看 `reporting/` 目录下的日志文件
- 处理时间取决于文档大小和复杂度

#### 3.5.3 输出文件说明

```
output/
├── artifacts/
│   ├── create_final_entities.parquet      # 实体数据
│   ├── create_final_relationships.parquet # 关系数据
│   ├── create_final_communities.parquet   # 社区数据
│   └── create_final_nodes.parquet         # 节点数据
└── graph.graphml                          # GraphML格式图谱
```

### 3.6 导出 Web 可视化格式

#### 3.6.1 Web 可视化方案选择

**推荐使用 JSON 格式 + JavaScript 可视化库**：

**格式选择理由**：

- **JSON**: 原生 JavaScript 支持，性能最佳，所有可视化库都支持
- **备选**: GEXF/GraphML（如需要与其他工具兼容）

**可视化库推荐**：

1. **Cytoscape.js** - 专业图谱可视化，功能强大
2. **D3.js** - 灵活性最高，自定义能力强
3. **vis.js** - 简单易用，快速上手

#### 3.6.2 JSON 格式转换脚本

创建 `export_to_web.py`:

```python
import pandas as pd
import json
import csv
from pathlib import Path

def export_to_web(output_dir="output"):
    """将GraphRAG输出转换为Web可视化格式"""

    print("正在导出Web可视化格式...")

    # 方案1: 使用GraphRAG输出数据
    if Path(f"{output_dir}/entities.parquet").exists():
        entities_df = pd.read_parquet(f"{output_dir}/entities.parquet")
        relationships_df = pd.read_parquet(f"{output_dir}/relationships.parquet")

        # 转换为JSON格式
        nodes = []
        for _, entity in entities_df.iterrows():
            nodes.append({
                "id": entity['title'],
                "label": entity['title'],
                "type": entity.get('type', 'entity'),
                "description": entity.get('description', ''),
                "size": len(entity.get('description', '')) / 10 + 20,
                "color": get_node_color(entity.get('type', 'entity'))
            })

        edges = []
        for _, rel in relationships_df.iterrows():
            edges.append({
                "source": rel['source'],
                "target": rel['target'],
                "weight": rel.get('weight', 1.0),
                "description": rel.get('description', ''),
                "type": rel.get('type', 'related')
            })

    # 方案2: 使用concepts.csv数据（如果GraphRAG数据不可用）
    else:
        print("使用concepts.csv创建图谱数据...")
        nodes, edges = create_from_concepts()

    # 创建完整的图谱数据结构
    graph_data = {
        "nodes": nodes,
        "edges": edges,
        "metadata": {
            "title": "Python函数知识图谱",
            "description": "基于第八章内容构建的Python函数概念图谱",
            "node_count": len(nodes),
            "edge_count": len(edges),
            "created_by": "GraphRAG + Web Visualization"
        }
    }

    # 导出JSON格式
    json_path = f"{output_dir}/knowledge_graph.json"
    with open(json_path, 'w', encoding='utf-8') as f:
        json.dump(graph_data, f, ensure_ascii=False, indent=2)

    # 同时导出Cytoscape.js专用格式
    cytoscape_data = {
        "elements": {
            "nodes": [{"data": node} for node in nodes],
            "edges": [{"data": edge} for edge in edges]
        }
    }

    cytoscape_path = f"{output_dir}/cytoscape_graph.json"
    with open(cytoscape_path, 'w', encoding='utf-8') as f:
        json.dump(cytoscape_data, f, ensure_ascii=False, indent=2)

    print(f"Web图谱已导出到:")
    print(f"  - 通用JSON: {json_path}")
    print(f"  - Cytoscape.js: {cytoscape_path}")
    print(f"节点数: {len(nodes)}, 边数: {len(edges)}")

    return json_path, cytoscape_path

def get_node_color(node_type):
    """根据节点类型返回颜色"""
    colors = {
        'entity': '#3498db',
        'person': '#e74c3c',
        'concept': '#2ecc71',
        'function': '#f39c12',
        'parameter': '#9b59b6',
        'default': '#95a5a6'
    }
    return colors.get(node_type, colors['default'])

def create_from_concepts():
    """从concepts.csv创建图谱数据"""
    concepts = []
    try:
        with open('concepts.csv', 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            for row in reader:
                if row and row[0].strip():
                    concepts.append(row[0].strip())
    except FileNotFoundError:
        print("未找到concepts.csv，使用默认概念")
        concepts = ['函数', '实参', '形参', '返回值', '位置实参', '关键字实参', '默认值']

    # 创建节点
    nodes = []
    for i, concept in enumerate(concepts):
        nodes.append({
            "id": concept,
            "label": concept,
            "type": classify_concept(concept),
            "description": f"{concept}的相关说明",
            "size": get_concept_size(concept),
            "color": get_concept_color(concept)
        })

    # 创建边（基于概念关系）
    edges = []
    relationships = [
        ('函数', '实参', 3.0), ('函数', '形参', 3.0), ('函数', '返回值', 3.0),
        ('实参', '位置实参', 2.0), ('实参', '关键字实参', 2.0),
        ('形参', '默认值', 2.0), ('实参', '形参', 2.0)
    ]

    for source, target, weight in relationships:
        if source in concepts and target in concepts:
            edges.append({
                "source": source,
                "target": target,
                "weight": weight,
                "description": f"{source}与{target}的关系",
                "type": "contains" if weight >= 2.5 else "related"
            })

    return nodes, edges

def classify_concept(concept):
    """分类概念类型"""
    if concept in ['函数']:
        return 'core'
    elif concept in ['实参', '形参']:
        return 'parameter'
    elif concept in ['位置实参', '关键字实参']:
        return 'param_type'
    else:
        return 'feature'

def get_concept_size(concept):
    """获取概念节点大小"""
    sizes = {
        '函数': 60, '实参': 45, '形参': 45,
        '位置实参': 35, '关键字实参': 35,
        '返回值': 40, '默认值': 30
    }
    return sizes.get(concept, 25)

def get_concept_color(concept):
    """获取概念节点颜色"""
    colors = {
        '函数': '#e74c3c',           # 红色
        '实参': '#3498db', '形参': '#3498db',  # 蓝色
        '位置实参': '#2ecc71', '关键字实参': '#2ecc71',  # 绿色
        '返回值': '#f39c12',         # 橙色
        '默认值': '#9b59b6'          # 紫色
    }
    return colors.get(concept, '#95a5a6')

if __name__ == "__main__":
    export_to_web()
```

#### 3.6.3 运行导出

```bash
python export_to_web.py
```

### 3.7 HTML+JavaScript 可视化

#### 3.7.1 创建 Web 可视化页面

```html
<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Python函数知识图谱</title>
    <script src="https://unpkg.com/cytoscape@3.26.0/dist/cytoscape.min.js"></script>
    <script src="https://unpkg.com/cytoscape-cose-bilkent@4.1.0/cytoscape-cose-bilkent.js"></script>
    <style>
      body {
        font-family: "Microsoft YaHei", Arial, sans-serif;
        margin: 0;
        padding: 20px;
        background-color: #f5f5f5;
      }

      .container {
        max-width: 1400px;
        margin: 0 auto;
        background: white;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        overflow: hidden;
      }

      .header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        text-align: center;
      }

      .controls {
        padding: 15px 20px;
        background: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        display: flex;
        gap: 15px;
        align-items: center;
        flex-wrap: wrap;
      }

      button {
        background: #007bff;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
      }

      #cy {
        width: 100%;
        height: 700px;
        background: #ffffff;
      }

      .info-panel {
        padding: 20px;
        background: #f8f9fa;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>🐍 Python函数知识图谱</h1>
        <p>基于concepts.csv构建的交互式概念图谱</p>
      </div>

      <div class="controls">
        <select id="layoutSelect">
          <option value="cose-bilkent">Cose-Bilkent (推荐)</option>
          <option value="circle">圆形布局</option>
          <option value="grid">网格布局</option>
        </select>
        <button onclick="resetLayout()">重置布局</button>
        <button onclick="fitToScreen()">适应屏幕</button>
        <button onclick="exportImage()">导出图片</button>
      </div>

      <div id="cy"></div>

      <div class="info-panel">
        <h4>🎨 节点颜色说明</h4>
        <p>
          🔴 核心概念 | 🔵 参数概念 | 🟢 参数类型 | 🟠 输出概念 | 🟣 特性概念
        </p>
      </div>
    </div>

    <script>
      let cy;

      // 示例数据（基于concepts.csv）
      const graphData = {
        elements: {
          nodes: [
            { data: { id: "函数", label: "函数", color: "#e74c3c", size: 60 } },
            { data: { id: "实参", label: "实参", color: "#3498db", size: 45 } },
            { data: { id: "形参", label: "形参", color: "#3498db", size: 45 } },
            {
              data: {
                id: "位置实参",
                label: "位置实参",
                color: "#2ecc71",
                size: 35,
              },
            },
            {
              data: {
                id: "关键字实参",
                label: "关键字实参",
                color: "#2ecc71",
                size: 35,
              },
            },
            {
              data: {
                id: "返回值",
                label: "返回值",
                color: "#f39c12",
                size: 40,
              },
            },
            {
              data: {
                id: "默认值",
                label: "默认值",
                color: "#9b59b6",
                size: 30,
              },
            },
          ],
          edges: [
            { data: { source: "函数", target: "实参", weight: 3 } },
            { data: { source: "函数", target: "形参", weight: 3 } },
            { data: { source: "函数", target: "返回值", weight: 3 } },
            { data: { source: "实参", target: "位置实参", weight: 2 } },
            { data: { source: "实参", target: "关键字实参", weight: 2 } },
            { data: { source: "形参", target: "默认值", weight: 2 } },
          ],
        },
      };

      // 初始化图谱
      function initGraph() {
        cy = cytoscape({
          container: document.getElementById("cy"),
          elements: graphData.elements,
          style: [
            {
              selector: "node",
              style: {
                "background-color": "data(color)",
                label: "data(label)",
                width: "data(size)",
                height: "data(size)",
                "font-size": "14px",
                "font-family": "Microsoft YaHei",
                "text-valign": "center",
                "text-halign": "center",
                color: "#333",
                "text-outline-width": 2,
                "text-outline-color": "#fff",
              },
            },
            {
              selector: "edge",
              style: {
                width: "mapData(weight, 1, 3, 2, 6)",
                "line-color": "#95a5a6",
                "curve-style": "bezier",
              },
            },
          ],
          layout: {
            name: "cose-bilkent",
            animate: true,
            fit: true,
            padding: 50,
          },
        });
      }

      function resetLayout() {
        cy.layout({ name: "cose-bilkent", animate: true, fit: true }).run();
      }

      function fitToScreen() {
        cy.fit();
      }

      function exportImage() {
        const png64 = cy.png({
          output: "blob",
          bg: "white",
          full: true,
          scale: 2,
        });
        const link = document.createElement("a");
        link.download = "knowledge_graph.png";
        link.href = URL.createObjectURL(png64);
        link.click();
      }

      document.addEventListener("DOMContentLoaded", initGraph);
    </script>
  </body>
</html>
```

#### 3.7.2 使用步骤

1. **运行导出脚本**：

   ```bash
   python export_to_web.py
   ```

2. **打开 HTML 文件**：

   - 双击 `knowledge_graph.html` 文件
   - 或在浏览器中打开该文件

3. **交互操作**：
   - **拖拽节点**: 重新排列图谱布局
   - **缩放**: 鼠标滚轮放大缩小
   - **选择布局**: 下拉菜单切换布局算法
   - **导出图片**: 保存高分辨率 PNG 图像

#### 3.7.3 可视化特性

**优势对比**：

- ✅ **无需安装软件** - 浏览器直接打开
- ✅ **完美中文支持** - 无编码问题
- ✅ **交互性强** - 实时拖拽和缩放
- ✅ **响应式设计** - 适配不同屏幕
- ✅ **易于分享** - 发送 HTML 文件即可
- ✅ **可定制性高** - 修改代码调整样式

**技术特点**：

- **Cytoscape.js**: 专业图谱可视化库
- **力导向布局**: 自动优化节点位置
- **颜色编码**: 按概念类型区分颜色
- **大小映射**: 节点大小反映重要性

## 4. 最佳实践

### 4.1 数据质量

- 确保输入文档质量高、结构清晰
- 定期清理和更新数据源
- 验证实体抽取的准确性

### 4.2 性能优化

- 合理设置 LLM 参数
- 使用缓存机制避免重复处理
- 分批处理大型文档集合

### 4.3 可视化技巧

- 根据分析目标选择合适的布局算法
- 使用颜色和大小编码传达信息
- 适当隐藏低权重边减少视觉混乱

## 5. 故障排除

### 5.1 常见问题

- **API 限制**: 检查 API 密钥和配额
- **内存不足**: 减少批处理大小
- **编码错误**: 确保文档 UTF-8 编码
- **导入失败**: 检查文件格式和路径

### 5.2 调试方法

- 查看 `reporting/` 目录下的日志
- 验证中间输出文件
- 使用小数据集测试流程

---

**注意**: 本 PRD 提供了最简单的实施路径，实际项目中可能需要根据具体需求进行调整和优化。
