{"elements": {"nodes": [{"data": {"id": "函数", "label": "函数", "type": "函数", "size": 60, "color": "#e74c3c", "description": "函数是一个具名的代码块，用于执行特定的任务。函数通过使用关键字（如 def）进行定义，可以接受参数（也称为参数或参数列表），这些参数可以有默认值。函数能够接收信息（参数），处理数据，并可选择性地返回结果值。函数的主要作用是使代码更加有组织、可读性更强，并且便于重用。通过将常用的操作封装在函数中，开发者可以在程序的不同部分多次调用同一个函数，从而避免重复编写相同的代码。此外，函数可以被存储在模块中，..."}}, {"data": {"id": "实参", "label": "实参", "type": "实参", "size": 45, "color": "#3498db", "description": "The term \"实参\" (actual argument) refers to the actual values or data provided to a function when it is called in programming. These arguments supply the necessary information that a function needs to p..."}}, {"data": {"id": "形参", "label": "形参", "type": "形参", "size": 45, "color": "#3498db", "description": "\"形参\" (formal parameter) refers to the variable names defined in a function's definition or signature that specify what kind of information or arguments the function can accept. These parameters act as..."}}, {"data": {"id": "位置实参", "label": "位置实参", "type": "", "size": 35, "color": "#2ecc71", "description": ""}}, {"data": {"id": "位置实参的顺序", "label": "位置实参的顺序", "type": "位置实参的顺序", "size": 25, "color": "#27ae60", "description": "The concept of \"位置实参的顺序\" (the order of positional arguments) refers to the sequence in which arguments are passed to a function during a function call. This order is crucial because each argument is a..."}}, {"data": {"id": "关键字实参", "label": "关键字实参", "type": "关键字实参", "size": 35, "color": "#2ecc71", "description": "The term \"关键字实参\" (keyword argument) refers to a method of passing arguments to a function in which each argument is explicitly associated with a parameter name. This approach allows the programmer to ..."}}, {"data": {"id": "默认值", "label": "默认值", "type": "默认值", "size": 30, "color": "#9b59b6", "description": "\"默认值\" (default value) refers to a value that is assigned to a parameter in a function definition. In programming, particularly in Python, default values make certain function arguments optional by pro..."}}, {"data": {"id": "返回值", "label": "返回值", "type": "返回值", "size": 40, "color": "#f39c12", "description": "A return value (返回值) is the value that a function returns to the caller after completing its execution. In programming, functions can return any type of value, including strings, lists, dictionaries, ..."}}, {"data": {"id": "while循环", "label": "while循环", "type": "WHILE循环", "size": 40, "color": "#1abc9c", "description": "The \"WHILE循环\" (while loop) is a control flow statement commonly used in programming, including in Python. It repeatedly executes a block of code as long as a specified condition remains true. This all..."}}, {"data": {"id": "列表", "label": "列表", "type": "列表", "size": 40, "color": "#f1c40f", "description": "A list (列表) is a fundamental data structure in Python that stores an ordered collection of items. Lists can hold various types of data, such as design names, messages, ingredients, or toppings, making..."}}], "edges": [{"data": {"id": "edge_0", "source": "函数", "target": "实参", "type": "contains", "weight": 3.0, "description": "函数 contains 实参"}}, {"data": {"id": "edge_1", "source": "函数", "target": "形参", "type": "contains", "weight": 3.0, "description": "函数 contains 形参"}}, {"data": {"id": "edge_2", "source": "函数", "target": "返回值", "type": "contains", "weight": 3.0, "description": "函数 contains 返回值"}}, {"data": {"id": "edge_3", "source": "实参", "target": "位置实参", "type": "contains", "weight": 2.0, "description": "实参 contains 位置实参"}}, {"data": {"id": "edge_4", "source": "实参", "target": "关键字实参", "type": "contains", "weight": 2.0, "description": "实参 contains 关键字实参"}}, {"data": {"id": "edge_5", "source": "位置实参", "target": "位置实参的顺序", "type": "contains", "weight": 2.0, "description": "位置实参 contains 位置实参的顺序"}}, {"data": {"id": "edge_6", "source": "形参", "target": "默认值", "type": "contains", "weight": 2.0, "description": "形参 contains 默认值"}}, {"data": {"id": "edge_7", "source": "实参", "target": "形参", "type": "corresponds", "weight": 2.0, "description": "实参 corresponds 形参"}}, {"data": {"id": "edge_8", "source": "位置实参", "target": "关键字实参", "type": "alternative", "weight": 1.5, "description": "位置实参 alternative 关键字实参"}}, {"data": {"id": "edge_9", "source": "函数", "target": "while循环", "type": "uses", "weight": 1.0, "description": "函数 uses while循环"}}, {"data": {"id": "edge_10", "source": "while循环", "target": "列表", "type": "processes", "weight": 1.0, "description": "while循环 processes 列表"}}]}}