{"elements": {"nodes": [{"data": {"id": "函数", "label": "函数", "type": "core", "size": 60, "color": "#e74c3c"}}, {"data": {"id": "实参", "label": "实参", "type": "param", "size": 45, "color": "#3498db"}}, {"data": {"id": "形参", "label": "形参", "type": "param", "size": 45, "color": "#3498db"}}, {"data": {"id": "位置实参", "label": "位置实参", "type": "param_type", "size": 35, "color": "#2ecc71"}}, {"data": {"id": "关键字实参", "label": "关键字实参", "type": "param_type", "size": 35, "color": "#2ecc71"}}, {"data": {"id": "默认值", "label": "默认值", "type": "feature", "size": 30, "color": "#9b59b6"}}, {"data": {"id": "返回值", "label": "返回值", "type": "output", "size": 40, "color": "#f39c12"}}, {"data": {"id": "while循环", "label": "while循环", "type": "control", "size": 40, "color": "#27ae60"}}, {"data": {"id": "列表", "label": "列表", "type": "data", "size": 40, "color": "#f1c40f"}}], "edges": [{"data": {"source": "函数", "target": "实参", "weight": 3.0}}, {"data": {"source": "函数", "target": "形参", "weight": 3.0}}, {"data": {"source": "函数", "target": "返回值", "weight": 3.0}}, {"data": {"source": "实参", "target": "位置实参", "weight": 2.0}}, {"data": {"source": "实参", "target": "关键字实参", "weight": 2.0}}, {"data": {"source": "形参", "target": "默认值", "weight": 2.0}}, {"data": {"source": "实参", "target": "形参", "weight": 2.0}}]}}