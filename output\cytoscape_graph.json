{"elements": {"nodes": [{"data": {"id": "函数", "label": "函数", "type": "函数", "size": 60, "color": "#e74c3c", "description": "函数是具有特定名称的代码块，用于执行特定任务。它们可以包含参数（如位置实参、关键字实参、列表等），通过调用时传递实参来实现灵活的功能，并且可以返回结果。函数的名称应具有描述性，便于理解和维护代码。每个函数应包含文档字符串注释，简要说明其功能，方便他人使用和维护。\n\n函数可以被模块包含，通过“模块名.函数名”的方式调用，也可以通过导入所有函数后直接调用。函数还可以被导入、重命名（指定别名），以便在不..."}}, {"data": {"id": "实参", "label": "实参", "type": "实参", "size": 45, "color": "#3498db", "description": "“实参”是指在调用函数时实际传递给函数的值，用于为函数的形参提供数据。实参可以有多种传递方式，主要包括位置实参和关键字实参。位置实参是按照参数在函数定义中出现的顺序进行传递的，而关键字实参则通过指定参数名进行传递，从而可以不按照顺序传递参数。实参的数量可以是任意的，具体取决于函数的定义和调用方式。\n\n在实际编程中，实参通常是在函数调用时直接给出的具体值。例如，在调用函数 mp(16, 'peppe..."}}, {"data": {"id": "形参", "label": "形参", "type": "形参", "size": 45, "color": "#3498db", "description": "“形参”是指在函数定义时括号内声明的变量名，用于接收调用函数时传递进来的实参。形参决定了函数可以接受哪些输入，是函数完成任务所需信息的占位符。例如，在函数定义如describe_pet(animal_type, pet_name)中，animal_type和pet_name就是形参；在print_models(unprinted_designs, completed_models)中，unprin..."}}, {"data": {"id": "位置实参", "label": "位置实参", "type": "位置实参", "size": 35, "color": "#2ecc71", "description": "“位置实参”是指在调用函数时，根据参数在函数定义中形参的顺序，依次传递给函数的实际参数。在 Python 中，位置实参要求实参的顺序必须与函数定义中的形参顺序一致。也就是说，函数调用时传入的第一个实参会赋值给第一个形参，第二个实参赋值给第二个形参，依此类推。例如，在调用函数 mp(16, 'pepperoni') 时，16 和 'pepperoni' 就是按照顺序传递的两个位置实参，分别对应函数定..."}}, {"data": {"id": "位置实参的顺序", "label": "位置实参的顺序", "type": "位置实参的顺序", "size": 25, "color": "#27ae60", "description": "“位置实参的顺序”指的是在函数调用时，实参（即实际传入的参数）必须按照函数定义中形参（即形式参数）出现的顺序依次传递。也就是说，实参的先后顺序需要与形参在函数定义中的顺序严格对应，每一个实参会按照其在调用时的位置，依次赋值给对应位置的形参。如果实参的顺序与形参的顺序不一致，或者没有按照定义顺序依次传递，就会导致参数对应错误，进而可能引发程序运行错误或逻辑错误。因此，正确理解和遵守“位置实参的顺序”..."}}, {"data": {"id": "关键字实参", "label": "关键字实参", "type": "关键字实参", "size": 35, "color": "#2ecc71", "description": "“关键字实参”是指在调用函数时，通过“参数名=值”的形式将实参传递给函数的方式。例如，可以写作 location='princeton' 或 field='physics'。与位置实参不同，关键字实参允许调用者在传递参数时不必按照函数定义中形参的顺序进行排列，而是通过明确指定参数名，将对应的值赋给相应的形参。这种方式不仅可以任意数量地传递参数，还能显著提高代码的可读性和灵活性。关键字实参常用于需要..."}}, {"data": {"id": "默认值", "label": "默认值", "type": "默认值", "size": 30, "color": "#9b59b6", "description": "“默认值”是指在函数定义时为形参指定的初始值或默认参数值。当调用函数时，如果没有为某个形参传递实参，则该形参会自动采用预先设定的默认值。这样，函数的某些参数在调用时可以省略，未传递时会自动使用默认值，从而提高了函数调用的灵活性和简便性。默认值的设置使得函数在定义时可以为形参指定一个合理的初始值，调用者在使用函数时可以选择性地传递参数，如果未传递，则由默认值补充。需要注意的是，只有在函数定义时为形参..."}}, {"data": {"id": "返回值", "label": "返回值", "type": "", "size": 40, "color": "#f39c12", "description": ""}}, {"data": {"id": "while循环", "label": "while循环", "type": "WHILE循环", "size": 40, "color": "#1abc9c", "description": "WHILE循环是一种常用的控制结构或循环结构，在编程中用于反复执行一段代码块，直到指定的条件不再满足为止。其基本原理是：在每次循环开始前，先判断条件是否成立，如果条件为真，则执行循环体内的代码；如果条件为假，则终止循环。while循环常用于需要重复执行某项任务的场景，例如处理列表中的所有元素、持续等待某个事件发生等。在实际应用中，如在print_models函数中，while循环可以不断从未打印设..."}}, {"data": {"id": "列表", "label": "列表", "type": "列表", "size": 40, "color": "#f1c40f", "description": "列表是Python中用于存储多个元素的数据结构。它可以包含任意数量的元素，并支持通过索引访问和操作其中的元素，常用于批量处理数据。在Python编程中，列表不仅可以作为实参传递给函数，也可以作为参数在函数内部进行遍历和处理。例如，unprinted_designs、completed_models 和 sent_messages 都是常见的列表，用于分别存储未打印的设计、已打印的模型以及已发送的信..."}}], "edges": [{"data": {"id": "edge_0", "source": "函数", "target": "实参", "type": "contains", "weight": 3.0, "description": "函数 contains 实参"}}, {"data": {"id": "edge_1", "source": "函数", "target": "形参", "type": "contains", "weight": 3.0, "description": "函数 contains 形参"}}, {"data": {"id": "edge_2", "source": "函数", "target": "返回值", "type": "contains", "weight": 3.0, "description": "函数 contains 返回值"}}, {"data": {"id": "edge_3", "source": "实参", "target": "位置实参", "type": "contains", "weight": 2.0, "description": "实参 contains 位置实参"}}, {"data": {"id": "edge_4", "source": "实参", "target": "关键字实参", "type": "contains", "weight": 2.0, "description": "实参 contains 关键字实参"}}, {"data": {"id": "edge_5", "source": "位置实参", "target": "位置实参的顺序", "type": "contains", "weight": 2.0, "description": "位置实参 contains 位置实参的顺序"}}, {"data": {"id": "edge_6", "source": "形参", "target": "默认值", "type": "contains", "weight": 2.0, "description": "形参 contains 默认值"}}, {"data": {"id": "edge_7", "source": "实参", "target": "形参", "type": "corresponds", "weight": 2.0, "description": "实参 corresponds 形参"}}, {"data": {"id": "edge_8", "source": "位置实参", "target": "关键字实参", "type": "alternative", "weight": 1.5, "description": "位置实参 alternative 关键字实参"}}, {"data": {"id": "edge_9", "source": "函数", "target": "while循环", "type": "uses", "weight": 1.0, "description": "函数 uses while循环"}}, {"data": {"id": "edge_10", "source": "while循环", "target": "列表", "type": "processes", "weight": 1.0, "description": "while循环 processes 列表"}}]}}