import pandas as pd
import networkx as nx
import json
from pathlib import Path

def create_gephi_optimized_graph():
    """创建专门为Gephi优化的知识图谱"""
    
    # 读取之前生成的图谱
    G = nx.read_graphml("output/enhanced_knowledge_graph.graphml")
    
    # 为Gephi优化节点属性
    node_colors = {
        'core_concept': '#FF6B6B',      # 红色 - 核心概念
        'parameter_concept': '#4ECDC4', # 青色 - 参数概念  
        'parameter_type': '#45B7D1',    # 蓝色 - 参数类型
        'control_structure': '#96CEB4', # 绿色 - 控制结构
        'data_structure': '#FFEAA7',    # 黄色 - 数据结构
        'feature': '#DDA0DD',           # 紫色 - 特性
        'output': '#FFB347',            # 橙色 - 输出
        'rule': '#F8BBD9'               # 粉色 - 规则
    }
    
    # 设置节点属性
    for node, data in G.nodes(data=True):
        node_type = data.get('type', 'concept')
        
        # 设置颜色
        data['color'] = node_colors.get(node_type, '#CCCCCC')
        
        # 设置大小（基于重要性和连接数）
        degree = G.degree(node)
        if node_type == 'core_concept':
            data['size'] = 60
        elif node_type in ['parameter_concept', 'control_structure', 'data_structure']:
            data['size'] = 45
        else:
            data['size'] = 30 + degree * 5
        
        # 设置标签
        data['label'] = node
        
        # 设置位置提示（用于初始布局）
        if node_type == 'core_concept':
            data['x'] = 0
            data['y'] = 0
        elif node_type == 'parameter_concept':
            data['x'] = -100 if '实参' in node else 100
            data['y'] = -50
        elif node_type == 'control_structure':
            data['x'] = -150
            data['y'] = 100
        elif node_type == 'data_structure':
            data['x'] = 150
            data['y'] = 100
    
    # 设置边属性
    edge_colors = {
        'contains': '#333333',      # 深灰色 - 包含关系
        'corresponds_to': '#E74C3C', # 红色 - 对应关系
        'alternative_to': '#3498DB', # 蓝色 - 替代关系
        'used_with': '#2ECC71',     # 绿色 - 使用关系
        'processes': '#F39C12',     # 橙色 - 处理关系
        'can_be': '#9B59B6'         # 紫色 - 可以是关系
    }
    
    for source, target, data in G.edges(data=True):
        rel_type = data.get('relationship', 'unknown')
        data['color'] = edge_colors.get(rel_type, '#CCCCCC')
        
        # 设置边的粗细
        weight = data.get('weight', 1.0)
        data['thickness'] = weight * 2
    
    # 导出优化后的图谱
    gexf_path = "output/optimized_knowledge_graph.gexf"
    
    with open(gexf_path, 'w', encoding='utf-8') as f:
        f.write('<?xml version="1.0" encoding="UTF-8"?>\n')
        gexf_content = '\n'.join(nx.generate_gexf(G))[39:]
        f.write(gexf_content)
    
    # 创建Gephi配置说明
    config_guide = {
        "节点颜色说明": {
            "红色": "核心概念（如：函数）",
            "青色": "参数概念（如：实参、形参）", 
            "蓝色": "参数类型（如：位置实参、关键字实参）",
            "绿色": "控制结构（如：while循环）",
            "黄色": "数据结构（如：列表）",
            "紫色": "特性（如：默认值）",
            "橙色": "输出（如：返回值）",
            "粉色": "规则（如：位置实参的顺序）"
        },
        "边颜色说明": {
            "深灰色": "包含关系",
            "红色": "对应关系", 
            "蓝色": "替代关系",
            "绿色": "使用关系",
            "橙色": "处理关系",
            "紫色": "可以是关系"
        },
        "推荐布局": "Force Atlas 2",
        "推荐字体": "Microsoft YaHei",
        "推荐字体大小": "14-18"
    }
    
    with open("output/gephi_config_guide.json", 'w', encoding='utf-8') as f:
        json.dump(config_guide, f, ensure_ascii=False, indent=2)
    
    print(f"优化的知识图谱已导出到: {gexf_path}")
    print(f"配置说明已保存到: output/gephi_config_guide.json")
    
    return gexf_path

if __name__ == "__main__":
    create_gephi_optimized_graph()
