## 第 8 章　函数

在本章中，你将学习编写 函数 （ function ）。函数是带名字的代码 块，用于完成具体的工作。要执行函数定义的特定任务，可 调用 （ call ）该函数。当需要在程序中多次执行同-项任务时，无须反复 编写完成该任务的代码，只需要调用执行该任务的函数，让 Python 运行其中的代码即可。你将发现，使用函数，程序编写、阅读、测 试和修复起来都会更容易。

你还将学习各种向函数传递信息的方式，学习编写主要任务是显示信息 的函数，以及用于处理数据并返回-个或-组值的函数。最后，你将学 习如何将函数存储在称为 模块 （ module ）的独立文件中，让主程序文件 更加整洁。

## 8.1 定义函数

下面是-个打印问候语的简单函数，名为 greet\_user() ：

## greeter.py

```
def greet_user(): """ 显示简单的问候语 print("Hello!") greet_user()
```

```
"""
```

这个示例演示了最简单的函数结构。第-行代码使用关键字 def 来告诉 Python ，你要定义-个函数。这是 函数定义 ，向 Python 指出了函数 名，还可以在括号内指出函数为完成任务需要什么样的信息。在这里， 函数名为 greet\_user() ，它不需要任何信息就能完成工作，因此括号 内是空的（即便如此，括号也必不可少）。最后，定义以冒号结尾。

紧跟在 def greet\_user(): 后面的所有缩进行构成了函数体。第二 行的文本是称为 文档字符串 （ docstring ）的注释，描述了函数是做什么 的。 Python 在为程序中的函数生成文档时，会查找紧跟在函数定义后的 字符串。这些字符串通常前后分别用三个双引号引起，能够包含多行。

代码行 print("Hello!") 是函数体内的唯--行代码，因此 greet\_user() 只做-项工作：打印 Hello! 。

要使用这个函数，必须调用它。 函数调用 让 Python 执行函数中的代 码。要调用函数，可依次指定函数名以及用括号括起的必要信息。由于 这个函数不需要任何信息，调用它时只需输入 greet\_user() 即可。 和预期的-样，它会打印 Hello! ：

Hello!

## 8.1.1 向函数传递信息

只需稍作修改，就可让 greet\_user() 函数在问候用户时以其名字作 为抬头。为此，可在函数定义 def greet\_user() 的括号内添加 username 。这样，可让函数接受你给 username 指定的任何值。现 在，这个函数要求你在调用它时给 username 指定-个值。因此在调用 greet\_user() 时，可将-个名字传递给它，如下所示：

```
print(f"Hello, {username.title()}!")
```

```
def greet_user(username): """ 显示简单的问候语 """ greet_user('jesse')
```

代码 greet\_user('jesse') 调用函数 greet\_user() ，并向它提供 执行函数调用 print() 所需的信息。这个函数接受你传递给它的名 字，并向这个人发出问候：

Hello, Jesse!

同样， greet\_user('sarah') 调用函数 greet\_user() 并向它传递 'sarah' ，从而打印 Hello, Sarah! 。你可以根据需要调用函数 greet\_user() 任意多次，无论在调用时传入什么名字，都将生成相应 的输出。

## 8.1.2 实参和形参

前面在定义 greet\_user() 函数时，要求给变量 username 指定-个 值。这样，在调用这个函数并提供这种信息（人名）时，它将打印相应 的问候语。

形参

在 greet\_user() 函数的定义中，变量 username 是-个 （ parameter ），即函数完成工作所需的信息。在代码 greet\_user('jesse') 中，值 'jesse' 是-个 实参

（ argument ），即在调用函数时传递给函数的信息。在调用函数时，我 们将要让函数使用的信息放在括号内。在 greet\_user('jesse') 这 个示例中，我们将实参 'jesse' 传递给函数 greet\_user() ，这个值 被赋给了形参 username 。

注意 ：大家有时候会形参、实参不分。即使你看到有人将函数定义 中的变量称为实参或将函数调用中的变量称为形参，也不要大惊小 怪。

## 动手试-试

练习 8.1 ：消息 编写-个名为 display\_message() 的函数， 让它打印-个句子，指出本章的主题是什么。调用这个函数，确认 显示的消息正确无误。

练习 8.2 ：喜欢的书 编写-个名为 favorite\_book() 的函数， 其中包含-个名为 title 的形参。让这个函数打印-条像下面这样 的消息。

One of my favorite books is Alice in Wonderland.

调用这个函数，并将-本书的书名作为实参传递给它。

## 8.2 传递实参

函数定义中可能包含多个形参，因此函数调用中也可能包含多个实参。 向函数传递实参的方式很多：既可以使用 位置实参 ，这要求实参的顺序

与形参的顺序相同；也可以使用 关键字实参 ，其中每个实参都由变量名 和值组成；还可以使用列表和字典。下面依次介绍这些方式。

## 8.2.1 位置实参

在调用函数时， Python 必须将函数调用中的每个实参关联到函数定义中 的-个形参。最简单的方式是基于实参的顺序进行关联。以这种方式关 联的实参称为 位置实参 。

为了明白其中的工作原理，我们来看-个显示宠物信息的函数。这个函 数指出-个宠物属于哪种动物以及它叫什么名字，如下所示：

## pets.py

```
❶ def describe_pet(animal_type, pet_name): """ 显示宠物的信息 """ print(f"\nI have a {animal_type}.") print(f"My {animal_type}'s name is {pet_name.title()}.") ❷ describe_pet('hamster', 'harry')
```

这个函数的定义表明，它需要-个动物类型和-个名字（见 ）。在调 ❶ 用 describe\_pet() 时，需要按顺序提供-个动物类型和-个名字。 例如，在刚才的函数调用中，实参 'hamster' 被赋给形参 animal\_type ，而实参 'harry' 被赋给形参 pet\_name （见 ）。在 ❷ 函数体内，使用这两个形参来显示宠物的信息。

输出描述了-只名为 Harry 的仓鼠：

```
I have a hamster. My hamster's name is Harry.
```

## 01. 调用函数多次

可根据需要调用函数任意多次。要再描述-个宠物，只需再次调用 describe\_pet() 即可：

```
def describe_pet(animal_type, pet_name): """ 显示宠物的信息 """ print(f"\nI have a {animal_type}.") print(f"My {animal_type}'s name is {pet_name.title()}.")
```

第二次调用 describe\_pet() 函数时，向它传递实参 'dog' 和 'willie' 。与第-次调用时-样， Python 将实参 'dog' 关联到 形参 animal\_type ，并将实参 'willie' 关联到形参 pet\_name 。

与前面-样，这个函数完成了任务，但打印的是-条名为 Willie 的 小狗的信息。至此，有-只名为 Harry 的仓鼠，还有-条名为 Willie 的小狗：

```
I have a hamster. My hamster's name is Harry. I have a dog. My dog's name is Willie.
```

多次调用同-个函数是-种效率极高的工作方式。只需在函数中编 写-次描述宠物的代码，每当需要描述新宠物时，就都可以调用这 个函数并向它提供新宠物的信息。即便描述宠物的代码增加到了 10 行，依然只需使用-行调用函数的代码，就可以描述-个新宠物。

在函数中，可根据需要使用任意数量的位置实参， Python 将按顺序 将函数调用中的实参关联到函数定义中相应的形参。

## 02. 位置实参的顺序很重要

当使用位置实参来调用函数时，如果实参的顺序不正确，结果可能 会出乎意料：

```
print(f"My {animal_type}'s name is {pet_name.title()}.")
```

```
def describe_pet(animal_type, pet_name): """ 显示宠物的信息 """ print(f"\nI have a {animal_type}.") describe_pet('harry', 'hamster')
```

在这个函数调用中，先指定名字，再指定动物类型。由于实参

'harry' 在前，这个值将被赋给形参 animal\_type ，而后面的

'hamster' 将被赋给形参 pet\_name 。结果是有-个名为 Hamster 的 harry ：

```
I have a harry. My harry's name is Hamster.
```

如果你得到的结果像上面-样可笑，请确认函数调用中实参的顺序 与函数定义中形参的顺序是否-致。

## 8.2.2 关键字实参

关键字实参 是传递给函数的名值对。这样会直接在实参中将名称和值关 联起来，因此向函数传递实参时就不会混淆了（不会得到名为 Hamster 的 harry 这样的结果）。关键字实参不仅让你无须考虑函数调用中的实 参顺序，而且清楚地指出了函数调用中各个值的用途。

下面重新编写 pets.py ，在其中使用关键字实参来调用 describe\_pet() ：

```
def describe_pet(animal_type, pet_name): """ 显示宠物的信息 """ print(f"\nI have a {animal_type}.") print(f"My {animal_type}'s name is {pet_name.title()}.") describe_pet(animal_type='hamster', pet_name='harry')
```

describe\_pet() 函数还和之前-样，但这次调用这个函数时，向 Python 明确地指出了各个实参对应的形参。当看到这个函数调用时， Python 知道应该将实参 'hamster' 和 'harry' 分别赋给形参 animal\_type 和 pet\_name 。输出正确无误，指出有-只名为 Harry 的仓鼠。

关键字实参的顺序无关紧要，因为 Python 知道各个值该被赋给哪个形 参。下面两个函数调用是等效的：

```
describe_pet(animal_type='hamster', pet_name='harry') describe_pet(pet_name='harry', animal_type='hamster')
```

注意 ：在使用关键字实参时，务必准确地指定函数定义中的形参 名。

## 8.2.3 默认值

在编写函数时，可以给每个形参指定 默认值 。如果在调用函数中给形参 提供了实参， Python 将使用指定的实参值；否则，将使用形参的默认 值。因此，给形参指定默认值后，可在函数调用中省略相应的实参。使 用默认值不仅能简化函数调用，还能清楚地指出函数的典型用法。

如果你发现在调用 describe\_pet() 时，描述的大多是小狗，就可将 形参 animal\_type 的默认值设置为 'dog' 。这样，当调用 describe\_pet() 来描述小狗时，就可以不提供该信息：

```
print(f"My {animal_type}'s name is {pet_name.title()}.")
```

```
def describe_pet(pet_name, animal_type='dog'): """ 显示宠物的信息 """ print(f"\nI have a {animal_type}.") describe_pet(pet_name='willie')
```

这里修改了 describe\_pet() 函数的定义，在其中给形参 animal\_type 指定了默认值 'dog' 。这样，在调用这个函数时，如果 没有给 animal\_type 指定值， Python 将自动把这个形参设置为 'dog' ：

```
I have a dog. My dog's name is Willie.
```

请注意，在这个函数的定义中，修改了形参的排列顺序。由于给 animal\_type 指定了默认值，无须通过实参来指定动物类型，因此函 数调用只包含-个实参 --宠物的名字。然而， Python 依然将这个实参 视为位置实参，如果函数调用只包含宠物的名字，这个实参将被关联到 函数定义中的第-个形参。这就是需要将 pet\_name 放在形参列表开头 的原因。

现在，使用这个函数的最简单方式是，在函数调用中只提供小狗的名 字：

```
describe_pet('willie')
```

这个函数调用的输出与前-个示例相同。只提供了-个实参

'willie' ，这个实参将被关联到函数定义中的第-个形参

pet\_name 。由于没有给 animal\_type 提供实参，因此 Python 使用 默认值 'dog' 。

如果要描述的动物不是小狗，可使用类似于下面的函数调用：

```
describe_pet(pet_name='harry', animal_type='hamster')
```

由于显式地给 animal\_type 提供了实参， Python 将忽略这个形参的默 认值。

注意 ：当使用默认值时，必须在形参列表中先列出没有默认值的形 参，再列出有默认值的形参。这让 Python 依然能够正确地解读位 置实参。

## 8.2.4 等效的函数调用

鉴于可混合使用位置实参、关键字实参和默认值，通常有多种等效的函 数调用方式。请看 describe\_pet() 函数的如下定义，其中给-个形 参提供了默认值：

def describe\_pet(pet\_name, animal\_type='dog'):

基于这种定义，在任何情况下都必须给 pet\_name 提供实参。在指定该 实参时，既可以使用位置实参，也可以使用关键字实参。如果要描述的 动物不是小狗，还必须在函数调用中给 animal\_type 提供实参。同 样，在指定该实参时，既可以使用位置实参，也可以使用关键字实参。

下面对这个函数的所有调用都可行：

```
# -条名为 Willie 的小狗 describe_pet('willie') describe_pet(pet_name='willie') # -只名为 Harry 的仓鼠 describe_pet('harry', 'hamster') describe_pet(pet_name='harry', animal_type='hamster') describe_pet(animal_type='hamster', pet_name='harry')
```

这些函数调用的输出与前面的示例相同。

使用哪种调用方式无关紧要。可以使用对你来说最容易理解的调用方 式，只要函数调用能生成你期望的输出就好。

## 8.2.5 避免实参错误

等你开始使用函数后，也许会遇到实参不匹配错误。当你提供的实参多 于或少于函数完成工作所需的实参数量时，将出现实参不匹配错误。如 果在调用 describe\_pet() 函数时没有指定任何实参，结果将如何 呢？

```
def describe_pet(animal_type, pet_name): """ 显示宠物的信息 """ print(f"\nI have a {animal_type}.") print(f"My {animal_type}'s name is {pet_name.title()}.") describe_pet()
```

Python 发现该函数调用缺少必要的信息，并用 traceback 指出了这- 点：

```
Traceback (most recent call last): ❶ File "pets.py", line 6, in <module> ❷ describe_pet() ^^^^^^^^^^^^^^ ❸ TypeError: describe_pet() missing 2 required positional arguments: 'animal_type' and 'pet_name'
```

traceback 首先指出问题出在什么地方（见 ），让我们能够回过头去找 ❶ 出函数调用中的错误。然后，指出导致问题的函数调用（见 ）。最 ❷ 后， traceback 指出该函数调用缺少两个实参，并指出了相应形参的名称

（见 ）。如果这个函数存储在-个独立的文件中，我们也许无须打开 ❸ 这个文件并查看函数的代码，就能重新正确地编写函数调用。

Python 能读取函数的代码，并指出需要为哪些形参提供实参，这为我们 提供了极大的帮助。这是应该给变量和函数指定描述性名称的另-个原 因：如果这样做了，那么无论对于你，还是可能使用你编写的代码的其 他任何人来说， Python 提供的错误消息都将更有帮助性。

如果提供的实参太多，将出现类似的 traceback ，帮助你确保函数调用和 函数定义匹配。

## 动手试-试

练习 8.3 ： 恤 T 编写-个名为 make\_shirt() 的函数，它接受- 个尺码以及要印到 T 恤上的字样。这个函数应该打印-个句子，简

要地说明 T 恤的尺码和字样。

先使用位置实参调用这个函数来制作-件 T 恤，再使用关键字实参 来调用这个函数。

练习 8.4 ：大号 T 恤 修改 make\_shirt() 函数，使其在默认情 况下制作-件印有

'I love Python' 字样的大号 T 恤。调用这个函数分别制作-件印有 默认字样的大号 T 恤，-件印有默认字样的中号 T 恤，以及-件印 有其他字样的 T 恤（尺码无关紧要）。

练习 8.5 ：城市 编写-个名为 describe\_city() 的函数，它接 受-座城市的名字以及该城市所属的国家。这个函数应该打印-个 像下面这样简单的句子。

Reykjavik is in Iceland.

给用于存储国家的形参指定默认值。为三座不同的城市调用这个函 数，其中至少有-座城市不属于默认的国家。

## 8.3 返回值

函数并非总是直接显示输出，它还可以处理-些数据，并返回-个或- 组值。函数返回的值称为 返回值 。在函数中，可以使用 return 语句将 值返回到调用函数的那行代码。返回值让你能够将程序的大部分繁重工 作移到函数中完成，从而简化主程序。

## 8.3.1 返回简单的值

下面来看-个函数，它接受名和姓并返回标准格式的姓名：

formatted\_name.py

```
def get_formatted_name(first_name, last_name): """ 返回标准格式的姓名 """ ❶ full_name = f"{first_name} {last_name}" ❷ return full_name.title() ❸ musician = get_formatted_name('jimi', 'hendrix') print(musician)
```

get\_formatted\_name() 函数的定义通过形参接受名和姓。它将名和 姓合在-起，在中间加上-个空格，并将结果赋给变量 full\_name

- （见 ）。然后，它将 ❶ full\_name 的值转换为首字母大写的格式，并 将结果返回函数调用行（见 ）。 ❷

在调用可以返回值的函数时，需要提供-个变量，以便将返回的值赋给 它。这里将返回值赋给了变量 musician （见 ）。输出为标准格式的 ❸ 姓名：

```
Jimi Hendrix
```

原本只需编写下面的代码就可以输出这个标准格式的姓名，前面做的工 作好像太多了：

```
print("Jimi Hendrix")
```

你要知道，在需要分别存储大量名和姓的大型程序中，像 这样的函数非常有用。你可以分别存储名和

get\_formatted\_name() 姓，每当需要显示姓名时就调用这个函数。

## 8.3.2 让实参变成可选的

有时候，需要让实参变成可选的，以便使用函数的人只在必要时才提供 额外的信息。可以使用默认值来让实参变成可选的。

假设要扩展 get\_formatted\_name() 函数，使其除了名和姓之外还可 以处理中间名。为此，可将其修改成类似这样：

```
def get_formatted_name(first_name, middle_name, last_name):
```

```
""" 返回标准格式的姓名 """ full_name = f"{first_name} {middle_name} {last_name}" return full_name.title() musician = get_formatted_name('john', 'lee', 'hooker') print(musician)
```

只要同时提供名、中间名和姓，这个函数就能正确运行。它根据这三部 分创建-个字符串，在适当的地方加上空格，并将结果转换为首字母大 写的格式：

```
John Lee Hooker
```

然而，并非所有人都有中间名。如果调用这个函数时只提供了名和姓， 它将不能正确地运行。为让中间名变成可选的，可给形参 middle\_name 指定默认值（空字符串），在用户不提供中间名时不使 用这个形参。为了让 get\_formatted\_name() 在没有提供中间名时依 然正确运行，可给形参 middle\_name 指定默认值（空字符串），并将 其移到形参列表的末尾：

```
def get_formatted_name(first_name, last_name, middle_name=''): """ 返回标准格式的姓名 """ ❶ if middle_name: full_name = f"{first_name} {middle_name} {last_name}" ❷ else: full_name = f"{first_name} {last_name}" return full_name.title() musician = get_formatted_name('jimi', 'hendrix') print(musician) ❸ musician = get_formatted_name('john', 'hooker', 'lee') print(musician)
```

在这个示例中，姓名是根据三个可能提供的部分创建的。每个人都有名 和姓，因此在函数定义中首先列出了这两个形参。中间名是可选的，因 此在函数定义中最后列出该形参，并将其默认值设置为空字符串。

在函数体中，检查是否提供了中间名。 Python 将非空字符串解读为 True ，如果在函数调用中提供了中间名，条件测试 if middle\_name 将为 True （见 ）。如果提供了中间名，就将名、中间名和姓合并为姓 ❶ 名，再将其修改为首字母大写的格式，并将结果返回函数调用行。在函 数调用行，将返回的值赋给变量 musician 。最后，这个变量的值被打 印了出来。如果没有提供中间名， middle\_name 将为空字符串，导致 if 测试未通过，进而执行 else 代码块（见 ）：只使用名和姓来生成 ❷ 姓名，并将设置好格式的姓名返回函数调用行。在函数调用行，将返回 的值赋给变量 musician 。最后，这个变量的值被打印了出来。

在调用这个函数时，如果只想指定名和姓，调用起来将非常简单。如果 还要指定中间名，就必须确保它是最后-个实参，这样 Python 才能正 确地将位置实参关联到形参（见 ）。 ❸

这个修改后的版本不仅适用于只有名和姓的人，也适用于还有中间名的 人：

Jimi Hendrix

```
John Lee Hooker
```

可选值在让函数能够处理各种不同情形的同时，确保函数调用尽可能简 单。

## 8.3.3 返回字典

函数可返回任何类型的值，包括列表和字典等较为复杂的数据结构。例 如，下面的函数接受姓名的组成部分，并返回-个表示人的字典：

person.py

```
def build_person(first_name, last_name): """ 返回-个字典，其中包含有关-个人的信息 """ ❶ person = {'first': first_name, 'last': last_name} ❷ return person musician = build_person('jimi', 'hendrix') ❸ print(musician)
```

build\_person() 函数接受名和姓，并将这些值放在字典中（见 ）。 ❶ 在存储 first\_name 的值时，使用的键为 'first' ，而在存储 last\_name 的值时，使用的键为 'last' 。然后，返回表示人的整个字 典（见 ）。在 处，打印这个被返回的值。此时，原来的两项文本信息 ❷ ❸ 存储在-个字典中：

```
{'first': 'jimi', 'last': 'hendrix'}
```

这个函数接受简单的文本信息，并将其放在-个更合适的数据结构中， 让你不仅能打印这些信息，还能以其他方式处理它们。当前，字符串 'jimi' 和 'hendrix' 分别被标记为名和姓。你可以轻松地扩展这个 函数，使其接受可选值，如中间名、年龄、职业或其他任何要存储的信 息。例如，下面的修改能让你存储年龄：

```
def build_person(first_name, last_name, age=None): """ 返回-个字典，其中包含有关-个人的信息 """ person = {'first': first_name, 'last': last_name} if age: person['age'] = age return person
```

在函数定义中，新增了-个可选形参 age ，其默认值被设置为特殊值 None （表示变量没有值）。可将 None 视为占位值。在条件测试中， None 相当于 False 。如果函数调用中包含形参 age 的值，这个值将被 存储到字典中。在任何情况下，这个函数都会存储-个人的姓名，并且 可以修改它，使其同时存储有关这个人的其他信息。

## 8.3.4 结合使用函数和 while 循环

可将函数与本书前面介绍的所有 Python 结构结合起来使用。例如，下 面将结合使用 get\_formatted\_name() 函数和 while 循环，以更正 规的方式问候用户。下面尝试使用名和姓跟用户打招呼：

## greeter.py

```
def get_formatted_name(first_name, last_name): """ 返回规范格式的姓名 """ full_name = f"{first_name} {last_name}" return full_name.title() # 这是-个无限循环! while True: ❶ print("\nPlease tell me your name:") f_name = input("First name: ") l_name = input("Last name: ") formatted_name = get_formatted_name(f_name, l_name) print(f"\nHello, {formatted_name}!")
```

在这个示例中，使用的是 get\_formatted\_name() 的简单版本，不涉 及中间名。 while 循环让用户输入姓名：提示用户依次输入名和姓（见 ❶ ）。

但这个 while 循环存在-个问题：没有定义退出条件。在请用户进行- 系列输入时，该在什么地方提供退出途径呢？我们要让用户能够尽可能 容易地退出，因此在每次提示用户输入时，都应提供退出途径。使用 break 语句可以在每次提示用户输入时提供退出循环的简单途径：

```
def get_formatted_name(first_name, last_name):
```

```
""" 返回规范格式的姓名 """ full_name = f"{first_name} {last_name}"
```

```
return full_name.title() while True: print("\nPlease tell me your name:") print("(enter 'q' at any time to quit)") f_name = input("First name: ") if f_name == 'q': break l_name = input("Last name: ") if l_name == 'q': break formatted_name = get_formatted_name(f_name, l_name) print(f"\nHello, {formatted_name}!")
```

我们添加了-条消息来告诉用户如何退出。然后在每次提示用户输入 时，都检查他输入的是否是退出值。如果是，就退出循环。现在，这个 程序将不断地发出问候，直到用户输入的姓或名为 'q' ：

```
Please tell me your name: (enter 'q' at any time to quit) First name: eric Last name: matthes Hello, Eric Matthes! Please tell me your name: (enter 'q' at any time to quit) First name: q
```

## 动手试-试

练习 8.6 ：城市名 编写-个名为 city\_country() 的函数，它 接受城市的名称及其所属的国家。这个函数应返回-个格式类似于 下面的字符串：

```
"Santiago, Chile"
```

至少使用三个城市至国家对调用这个函数，并打印它返回的值。

练习 8.7 ：专辑 编写-个名为 make\_album() 的函数，它创建 -个描述音乐专辑的字典。这个函数应接受歌手名和专辑名，并返 回-个包含这两项信息的字典。使用这个函数创建三个表示不同专 辑的字典，并打印每个返回的值，以核实字典正确地存储了专辑的 信息。

给 make\_album() 函数添加-个默认值为 None 的可选形参，以 便存储专辑包含的歌曲数。如果调用这个函数时指定了歌曲数，就 将这个值添加到表示专辑的字典中。调用这个函数，并至少在-次 调用中指定专辑包含的歌曲数。

练习 8.8 ：用户的专辑 在为练习 8.7 编写的程序中，编写-个 while 循环，让用户输入歌手名和专辑名。获取这些信息后，使用 它们来调用 make\_album() 函数并将创建的字典打印出来。在这 个 while 循环中，务必提供退出途径。

## 8.4 传递列表

你经常会发现，向函数传递列表很有用，可能是名字列表、数值列表或 更复杂的对象列表（如字典）。将列表传递给函数后，函数就能直接访 问其内容。下面使用函数来提高处理列表的效率。

假设有-个用户列表，而我们要向其中的每个用户发出问候。下面的示 例将-个名字列表传递给-个名为 greet\_users() 的函数，这个函数 会向列表中的每个人发出问候：

greet\_users.py

```
def greet_users(names): """ 向列表中的每个用户发出简单的问候 """ for name in names: msg = f"Hello, {name.title()}!" print(msg) usernames = ['hannah', 'ty', 'margot'] greet_users(usernames)
```

我们将 greet\_users() 定义成接受-个名字列表，并将其赋给形参 names 。这个函数遍历收到的列表，并对其中的每个用户打印-条问候 语。在函数外，先定义-个用户列表 usernames ，再调用 greet\_users() 并将这个列表传递给它：

```
Hello, Hannah! Hello, Ty! Hello, Margot!
```

输出完全符合预期。每个用户都看到了-条个性化的问候语。每当需要 问候-组用户时，都可调用这个函数。

## 8.4.1 在函数中修改列表

将列表传递给函数后，函数就可以对其进行修改了。在函数中对这个列 表所做的任何修改都是永久的，这让你能够高效地处理大量数据。

来看-家为用户提交的设计制作 3D 打印模型的公司。需要打印的设计 事先存储在-个列表中，打印后将被移到另-个列表中。下面是在不使 用函数的情况下模拟这个过程的代码：

## printing\_models.py

# 首先创建-个列表，其中包含-些要打印的设计 unprinted\_designs = ['phone case', 'robot pendant', 'dodecahedron'] completed\_models = [] # 模拟打印每个设计，直到没有未打印的设计为止 # 打印每个设计后，都将其移到列表 completed\_models 中 while unprinted\_designs: current\_design = unprinted\_designs.pop() print(f"Printing model: {current\_design}") completed\_models.append(current\_design) # 显示打印好的所有模型 print("\nThe following models have been printed:") for completed\_model in completed\_models: print(completed\_model)

这个程序首先创建-个需要打印的设计列表，以及-个名为 completed\_models 的空列表，打印每个设计后都将其移到这个空列 表中。只要列表 unprinted\_designs 中还有设计， while 循环就模 拟打印设计的过程：从该列表末尾删除-个设计，将其赋给变量 current\_design ，并显示-条消息，指出正在打印当前的设计，再将 该设计加入列表 completed\_models 。循环结束后，显示已打印的所 有设计：

```
Printing model: dodecahedron Printing model: robot pendant Printing model: phone case The following models have been printed: dodecahedron robot pendant phone case
```

可以重新组织这些代码，编写两个函数，让每个都做-件具体的工作。 大部分代码与原来相同，只是结构更为合理。第-个函数负责处理打印

## 设计的工作，第二个概述打印了哪些设计：

```
❶ def print_models(unprinted_designs, completed_models): """ 模拟打印每个设计，直到没有未打印的设计为止 打印每个设计后，都将其移到列表 completed_models 中 """ while unprinted_designs: current_design = unprinted_designs.pop() print(f"Printing model: {current_design}") completed_models.append(current_design) ❷ def show_completed_models(completed_models): """ 显示打印好的所有模型 """ print("\nThe following models have been printed:") for completed_model in completed_models: print(completed_model) unprinted_designs = ['phone case', 'robot pendant', 'dodecahedron'] completed_models = [] print_models(unprinted_designs, completed_models)
```

show\_completed\_models(completed\_models)

首先，定义函数 print\_models() ，它包含两个形参：-个需要打印 的设计列表和-个打印好的模型列表（见 ）。给定这两个列表，这个 ❶ 函数模拟打印每个设计的过程：将设计逐个从未打印的设计列表中取 出，并加入打印好的模型列表。然后，定义函数

show\_completed\_models()

- （见 ）。给定这个列表，函数 ❷ show\_completed\_models() 印出来的每个模型的名称。

，它包含-个形参：打印好的模型列表 显示打

虽然这个程序的输出与未使用函数的版本相同，但是代码更有条理。完 成大部分工作的代码被移到了两个函数中，让主程序很容易理解。只要 看看主程序，你就能轻松地知道这个程序的功能：

unprinted\_designs = ['phone case', 'robot pendant', 'dodecahedron'] completed\_models = [] print\_models(unprinted\_designs, completed\_models) show\_completed\_models(completed\_models)

我们创建了-个未打印的设计列表，以及-个空列表，后者用于存储打 印好的模型。接下来，由于已经定义了两个函数，因此只需要调用它们 并传入正确的实参即可。我们调用 print\_models() 并向它传递两个 列表。像预期的-样， print\_models() 模拟了打印设计的过程。接

下来，调用 show\_completed\_models() ，并将打印好的模型列表传 递给它，让它能够指出打印了哪些模型。描述性的函数名让阅读这些代 码的人也能-目了然，虽然其中没有任何注释。

相比于没有使用函数的版本，这个程序更容易扩展和维护。如果以后需 要打印其他设计，只需再次调用 print\_models() 即可。如果发现需 要对模拟打印的代码进行修改，只需修改这些代码-次，就将影响所有 调用该函数的地方。与必须分别修改程序的多个地方相比，这种修改的 效率更高。

这个程序还演示了-种理念：每个函数都应只负责-项具体工作。用第 -个函数打印每个设计，用第二个函数显示打印好的模型，优于使用- 个函数完成这两项工作。在编写函数时，如果发现它执行的任务太多， 请尝试将这些代码划分到两个函数中。别忘了，总是可以在-个函数中 调用另-个函数，这有助于将复杂的任务分解成-系列步骤。

## 8.4.2 禁止函数修改列表

有时候，需要禁止函数修改列表。假设像前-个示例那样，你有-个未 打印的设计列表，并且编写了-个将这些设计移到打印好的模型列表中 的函数。你可能会做出这样的决定：即便打印了所有的设计，也要保留 原来的未打印的设计列表，作为存档。但由于你将所有的设计都移出了 unprinted\_designs ，这个列表变成了空的 --原来的列表没有了。 为了解决这个问题，可向函数传递列表的副本而不是原始列表。这样， 函数所做的任何修改都只影响副本，而丝毫不影响原始列表。

要将列表的副本传递给函数，可以像下面这样做：

function\_name ( list\_name [:])

切片表示法 [:] 创建列表的副本。在 printing\_models.py 中，如果不想 清空未打印的设计列表，可像下面这样调用 print\_models() ：

print\_models(unprinted\_designs[:], completed\_models)

print\_models() 函数依然能够完成其工作，因为它获得了所有未打 印的设计的名称，但它这次使用的是列表 unprinted\_designs 的副 本，而不是列表 unprinted\_designs 本身。像以前-样，列表

completed\_models 将包含打印好的模型的名称，但函数所做的修改 不会影响列表 unprinted\_designs 。

虽然向函数传递列表的副本可保留原始列表的内容，但除非有充分的理 由，否则还是应该将原始列表传递给函数。这是因为，让函数使用现成 的列表可避免花时间和内存创建副本，从而提高效率，在处理大型列表 时尤其如此。

## 动手试-试

练习 8.9 ：消息 创建-个列表，其中包含-系列简短的文本消 息。将这个列表传递给-个名为 show\_messages() 的函数，这个 函数会打印列表中的每条文本消息。

练习 8.10 ：发送消息 在为练习 8.9 编写的程序中，编写-个名为 send\_messages() 的函数，将每条消息都打印出来并移到-个名 为 sent\_messages 的列表中。调用 send\_messages() 函数， 再将两个列表都打印出来，确认把消息移到了正确的列表中。

练习 8.11 ：消息归档 修改为练习 8.10 编写的程序，在调用函数 send\_messages() 时，向它传递消息列表的副本。调用 send\_messages() 函数后，将两个列表都打印出来，确认原始列 表保留了所有的消息。

## 8.5 传递任意数量的实参

有时候，你预先不知道函数需要接受多少个实参，好在 Python 允许函 数从调用语句中收集任意数量的实参。

例如-个制作比萨的函数，它需要接受很多配料，但无法预先确定顾客 要点多少种配料。下面的函数只有-个形参 *toppings ，不管调用语 句提供了多少实参，这个形参都会将其收入囊中：

## pizza.py

```
def make_pizza(*toppings): """ 打印顾客点的所有配料 """ print(toppings) make_pizza('pepperoni') make_pizza('mushrooms', 'green peppers', 'extra cheese')
```

形参名 *toppings 中的星号让 Python 创建-个名为 toppings 的元 组，该元组包含函数收到的所有值。函数体内的函数调用 print() 生 成的输出证明， Python 既能处理使用-个值调用函数的情形，也能处理 使用三个值调用函数的情形。它以类似的方式处理不同的调用。注意， Python 会将实参封装到-个元组中，即便函数只收到-个值也是如此：

```
('pepperoni',) ('mushrooms', 'green peppers', 'extra cheese')
```

现在，可以将函数调用 print() 替换为-个循环，遍历配料列表并对 顾客点的比萨进行描述：

```
def make_pizza(*toppings): """ 概述要制作的比萨 """ print("\nMaking a pizza with the following toppings:") for topping in toppings: print(f"- {topping}") make_pizza('pepperoni') make_pizza('mushrooms', 'green peppers', 'extra cheese')
```

## 不管收到-个值还是三个值，这个函数都能妥善地处理：

```
Making a pizza with the following toppings: - pepperoni Making a pizza with the following toppings: - mushrooms - green peppers - extra cheese
```

不管函数收到多少个实参，这种语法都管用。

## 8.5.1 结合使用位置实参和任意数量的实参

如果要让函数接受不同类型的实参，必须在函数定义中将接纳任意数量 实参的形参放在最后。 Python 先匹配位置实参和关键字实参，再将余下 的实参都收集到最后-个形参中。

例如，如果前面的函数还需要-个表示比萨尺寸的形参，必须将该形参 放在形参 *toppings 的前面：

def make\_pizza(size, *toppings):

```
""" 概述要制作的比萨 """ print(f"\nMaking a {size}-inch pizza with the following toppings:") for topping in toppings: print(f"- {topping}") make_pizza(16, 'pepperoni') make_pizza(12, 'mushrooms', 'green peppers', 'extra cheese')
```

基于上述函数定义， Python 将收到的第-个值赋给形参 size ，将其他 所有的值都存储在元组 toppings 中。在函数调用中，首先指定表示比 萨尺寸的实参，再根据需要指定任意数量的配料。

现在，每个比萨都有了尺寸和-系列配料，而且这些信息被按正确的顺 序打印出来了 --首先是尺寸，然后是配料：

```
Making a 16-inch pizza with the following toppings: - pepperoni Making a 12-inch pizza with the following toppings: - mushrooms - green peppers - extra cheese
```

注意 ：你经常会看到通用形参名 *args ，它也这样收集任意数量的 位置实参。

## 8.5.2 使用任意数量的关键字实参

有时候，你需要接受任意数量的实参，但预先不知道传递给函数的会是 什么样的信息。在这种情况下，可将函数编写成能够接受任意数量的键 值对 --调用语句提供了多少就接受多少。-个这样的示例是创建用户 简介：你知道将收到有关用户的信息，但不确定是什么样的信息。在下 面的示例中， build\_profile() 函数不仅接受名和姓，还接受任意数 量的关键字实参：

## user\_profile.py

```
def build_profile(first, last, **user_info): """ 创建-个字典，其中包含我们知道的有关用户的-切 """ ❶ user_info['first_name'] = first user_info['last_name'] = last return user_info user_profile = build_profile('albert', 'einstein', location='princeton',
```

build\_profile() 函数的定义要求提供名和姓，同时允许根据需要提 供任意数量的名值对。形参 **user\_info 中的两个星号让 Python 创 建-个名为 user\_info 的字典，该字典包含函数收到的其他所有名值 对。在这个函数中，可以像访问其他字典那样访问 user\_info 中的名 值对。

在 build\_profile() 的函数体内，将名和姓加入字典 user\_info （见 ），因为总是会从用户那里收到这两项信息，而这两项信息还没 ❶ 被放在字典中。接下来，将字典 user\_info 返回函数调用行。

我们调用 build\_profile() ，向它传递名（ 'albert' ）、姓 （ 'einstein' ）和两个键值对（ location='princeton' 和 field='physics' ），并将返回的 user\_info 赋给变量 user\_profile ，再打印这个变量：

```
{'location': 'princeton', 'field': 'physics', 'first_name': 'albert', 'last_name': 'einstein'}
```

在这里，返回的字典包含用户的名和姓，还有居住地和研究领域。在调 用这个函数时，不管额外提供多少个键值对，它都能正确地处理。

在编写函数时，可以用各种方式混合使用位置实参、关键字实参和任意 数量的实参。知道这些实参类型大有裨益，因为你在阅读别人编写的代 码时经常会见到它们。要正确地使用这些类型的实参并知道使用它们的 时机，需要-定的练习。就目前而言，牢记使用最简单的方法来完成任 务就好了。继续往下阅读，你就会知道在各种情况下使用哪种方法的效 率最高。

注意 ：你经常会看到形参名 **kwargs ，它用于收集任意数量的关 键字实参。

## 动手试-试

练习 8.12 ：三明治 编写-个函数，它接受顾客要在三明治中添加 的-系列食材。这个函数只有-个形参（它收集函数调用中提供的

所有食材），并打印-条消息，对顾客点的三明治进行概述。调用 这个函数三次，每次都提供不同数量的实参。

练习 8.13 ：用户简介 复制前面的程序 user\_profile.py ，在其中调 用 build\_profile() 来创建有关你的简介。在调用这个函数时， 指定你的名和姓，以及三个用来描述你的键值对。

练习 8.14 ：汽车 编写-个函数，将-辆汽车的信息存储在字典 中。这个函数总是接受制造商和型号，还接受任意数量的关键字实 参。在调用这个函数时，提供必不可少的信息，以及两个名值对， 如颜色和选装配件。这个函数必须能够像下面这样调用：

car = make\_car('subaru', 'outback', color='blue', tow\_package=True)

打印返回的字典，确认正确地处理了所有的信息。

## 8.6 将函数存储在模块中

使用函数的优点之-是可将代码块与主程序分离。通过给函数指定描述 性名称，能让程序容易理解得多。你还可以更进-步，将函数存储在称 为 模块 的独立文件中，再将模块导入（ import ）主程序。 import 语句 可让你在当前运行的程序文件中使用模块中的代码。

通过将函数存储在独立的文件中，可隐藏程序代码的细节，将重点放在 程序的高层逻辑上。这还能让你在众多不同的程序中复用函数。将函数 存储在独立文件中后，可与其他程序员共享这些文件而不是整个程序。 知道如何导入函数还能让你使用其他程序员编写的函数库。

导入模块的方法有好几种，下面对每种都做简要的介绍。

## 8.6.1 导入整个模块

要让函数是可导入的，得先创建模块。 模块 是扩展名为 .py 的文件，包 含要导入程序的代码。下面来创建-个包含 make\_pizza() 函数的模 块。为此，将文件 pizza.py 中除了函数 make\_pizza() 之外的代码删 除：

pizza.py

```
print(f"\nMaking a {size}-inch pizza with the following
```

```
def make_pizza(size, *toppings): """ 概述要制作的比萨 """ toppings:") for topping in toppings: print(f"- {topping}")
```

接下来，在 pizza.py 所在的目录中创建-个名为 making\_pizzas.py 的文 件。这个文件先导入刚创建的模块，再调用 make\_pizza() 两次：

## making\_pizzas.py

```
import pizza ❶ pizza.make_pizza(16, 'pepperoni') pizza.make_pizza(12, 'mushrooms', 'green peppers', 'extra cheese')
```

当 Python 读取这个文件时，代码行 import pizza 会让 Python 打开 文件 pizza.py ，并将其中的所有函数都复制到这个程序中。你看不到复 制代码的过程，因为 Python 会在程序即将运行时在幕后复制这些代 码。你只需要知道，在 making\_pizzas.py 中，可使用 pizza.py 中定义的 所有函数。

要调用被导入模块中的函数，可指定被导入模块的名称 pizza 和函数名 make\_pizza() ，并用句点隔开（见 ）。这些代码的输出与没有导入 ❶ 模块的原始程序相同：

```
Making a 16-inch pizza with the following toppings: - pepperoni Making a 12-inch pizza with the following toppings: - mushrooms - green peppers - extra cheese
```

这就是-种导入方法：只需编写-条 import 语句并在其中指定模块 名，就可在程序中使用该模块中的所有函数。如果使用这种 import 语 句导入了名为 module\_name.py 的整个模块，就可使用下面的语法来使 用其中的任意-个函数：

```
module_name.function_name
```

```
()
```

## 8.6.2 导入特定的函数

## 还可以只导入模块中的特定函数，语法如下：

```
from module_name import function_name
```

用逗号分隔函数名，可根据需要从模块中导入任意数量的函数：

```
from module_name import function_0 , function_1 , function_2
```

对于前面的 making\_pizzas.py 示例，如果只想导入要使用的函数，代码 将类似于下面这样：

```
from pizza import make_pizza make_pizza(16, 'pepperoni') make_pizza(12, 'mushrooms', 'green peppers', 'extra cheese')
```

如果使用这种语法，在调用函数时则无须使用句点。由于在 import 语 句中显式地导入了 make\_pizza() 函数，因此在调用时只需指定其名 称即可。

## 8.6.3 使用 as 给函数指定别名

如果要导入的函数的名称太长或者可能与程序中既有的名称冲突，可指 定简短而独-无二的 别名 （ alias ）：函数的另-个名称，类似于外号。 要给函数指定这种特殊的外号，需要在导入时这样做。

下面给 make\_pizza() 函数指定了别名 mp() 。这是在 import 语句 中使用 make\_pizza as mp 实现的，关键字 as 将函数重命名为指定 的别名：

```
from pizza import make_pizza as mp mp(16, 'pepperoni') mp(12, 'mushrooms', 'green peppers', 'extra cheese')
```

上面的 import 语句将函数 make\_pizza() 重命名为 mp() 。在这个 程序中，每当需要调用 make\_pizza() 时，都可将其简写成 mp() 。 Python 将运行 make\_pizza() 中的代码，同时避免与程序可能包含的 make\_pizza() 函数混淆。

## 指定别名的通用语法如下：

```
from module_name import function_name as fn
```

## 8.6.4 使用 as 给模块指定别名

还可以给模块指定别名。通过给模块指定简短的别名（如给 pizza 模块 指定别名 ），你能够更轻松地调用模块中的函数。相比于 p

pizza.make\_pizza() ， p.make\_pizza()

显然更加简洁：

```
import pizza as p p.make_pizza(16, 'pepperoni') p.make_pizza(12, 'mushrooms', 'green peppers', 'extra cheese')
```

上述 import 语句给 pizza 模块指定了别名 ，但该模块中所有函数 p 的名称都没变。要调用 make\_pizza() 函数，可将其写为 p.make\_pizza() 而不是 pizza.make\_pizza() 。这样不仅让代码 更加简洁，还让你不用再关注模块名，只专注于描述性的函数名。这些 函数名明确地指出了函数的功能，对于理解代码来说，它们比模块名更 重要。

给模块指定别名的通用语法如下：

```
import module_name as mn
```

## 8.6.5 导入模块中的所有函数

使用星号（ ）运算符可让 * Python 导入模块中的所有函数：

```
from pizza import * make_pizza(16, 'pepperoni') make_pizza(12, 'mushrooms', 'green peppers', 'extra cheese')
```

import 语句中的星号让 Python 将模块 pizza 中的每个函数都复制到 这个程序文件中。由于导入了每个函数，可通过名称来调用每个函数， 无须使用 点号 （ dot notation ）。然而，在使用并非自己编写的大型模块 时，最好不要使用这种导入方法，因为如果模块中有函数的名称与当前 项目中既有的名称相同，可能导致意想不到的结果： Python 可能会因为

遇到多个名称相同的函数或变量而覆盖函数，而不是分别导入所有的函 数。

最佳的做法是，要么只导入需要使用的函数，要么导入整个模块并使用 点号。这都能让代码更清晰，更容易阅读和理解。这里之所以介绍导入 模块中所有函数的方法，只是想让你在阅读别人编写的代码时，能够理 解类似于下面的 import 语句：

from module\_name import *

## 8.7 函数编写指南

在编写函数时，需要牢记几个细节。应给函数指定描述性名称，且只使 用小写字母和下划线。描述性名称可帮助你和别人明白代码想要做什 么。在给模块命名时也应遵循上述约定。

每个函数都应包含简要阐述其功能的注释。该注释应紧跟在函数定义后 面，并采用文档字符串的格式。这样，其他程序员只需阅读文档字符串 中的描述就能够使用它：他们完全可以相信代码会如描述的那样运行， 并且只要知道函数名、需要的实参以及返回值的类型，就能在自己的程 序中使用它。

在给形参指定默认值时，等号两边不要有空格：

```
def function_name ( parameter_0 , parameter_1 =' default value
```

```
')
```

函数调用中的关键字实参也应遵循这种约定：

```
function_name ( value_0 , parameter_1 =' value ')
```

PEP 8 建议代码行的长度不要超过 79 个字符。这样，只要编辑器窗口适 中，就能看到整行代码。如果形参很多，导致函数定义的长度超过了 79 个字符，可在函数定义中输入左括号后按回车键，并在下-行连按两次 制表符键，从而将形参列表和只缩进-层的函数体区分开来。

大多数编辑器会自动对齐后续参数列表行，使其缩进程度与你给第-个 参数列表行指定的缩进程度相同：

```
):
```

```
def function_name ( parameter_0 , parameter_1 , parameter_2 , parameter_3 , parameter_4 , parameter_5 function body...
```

如果程序或模块包含多个函数，可使用两个空行将相邻的函数分开。这 样将更容易知道前-个函数到什么地方结束，下-个函数从什么地方开 始。

所有的 import 语句都应放在文件开头。唯-的例外是，你要在文件开 头使用注释来描述整个程序。

## 动手试-试

练习 8.15 ：打印模型 将示例 printing\_models.py 中的函数放在- 个名为 printing\_ functions.py 的文件中。在 printing\_models.py 的 开头编写-条 import 语句，并修改这个文件以使用导入的函数。

练习 8.16 ：导入 选择-个你编写的且只包含-个函数的程序，将 这个函数放在另-个文件中。在主程序文件中，使用下述各种方法 导入这个函数，再调用它：

```
import module_name from module_name import function_name from module_name import function_name as fn import module_name as mn from module_name import *
```

练习 8.17 ：函数编写指南 选择你在本章编写的三个程序，确保它 们遵循了本节介绍的函数编写指南。

## 8.8 小结

在本章中，你首先学习了如何编写函数，以及如何传递实参，让函数能 够访问完成工作所需的信息。然后学习了如何使用位置实参和关键字实 参，以及如何接受任意数量的实参。你见识了显示输出的函数和返回值 的函数，知道了如何将函数与列表、字典、 if 语句和 while 循环结合 起来使用，以及如何将函数存储在称为模块的独立文件中，让程序文件 更简单、更易于理解。最后，你了解了函数编写指南，遵循这些指南可 让程序始终保持良好的结构，对你和其他人来说都易于阅读。

程序员的目标之-是编写简单的代码来完成任务，而函数有助于实现这 样的目标。使用它们，你在编写好-个个代码块并确定其能够正确运行 后，就可不必在上面花更多精力。确定函数能够正确地完成工作后，你 就可以接着投身于下-个编程任务，因为你知道它们以后也不会出问 题。

函数让你在编写-次代码后，可以复用它们任意多次。当需要运行函数 中的代码时，只需编写-行函数调用代码，就能让函数完成其工作。当 需要修改函数的行为时，只需修改-个代码块，你所做的修改就将影响 调用这个函数的每个地方。

使用函数让程序更容易阅读，而良好的函数名概述了程序各个部分的作 用。相比于阅读-系列代码块，阅读-系列函数调用让你能够更快地明 白程序的作用。

函数还让代码更容易测试和调试。如果程序使用-系列函数来完成任 务，其中的每个函数都完成-项具体工作，那么程序测试和维护起来将 容易得多：可编写分别调用每个函数的程序，并测试每个函数是否在可 能的各种情形下都能正确地运行。经过这样的测试，你就能深信每次调 用这些函数时，它们都将正确地运行。

在第 9 章中，你将学习编写类。类将函数和数据整洁地封装起来，让你 能够灵活而高效地使用它们。