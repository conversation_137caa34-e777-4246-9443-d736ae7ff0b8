import pandas as pd

# 读取实体数据
df = pd.read_parquet('output/entities.parquet')
print('总实体数量:', len(df))

print('\n您的10个概念在实体中的情况:')
concepts = ['函数', '实参', '形参', '位置实参', '位置实参的顺序', '关键字实参', '默认值', '返回值', 'while循环', '列表']

for concept in concepts:
    matches = df[df['title'] == concept]
    print(f'{concept}: {len(matches)} 个实体')

print('\n所有实体类型统计:')
print(df['type'].value_counts())

print('\n包含您概念的实体详情:')
for concept in concepts:
    matches = df[df['title'] == concept]
    if len(matches) > 0:
        print(f'\n{concept}:')
        for _, row in matches.iterrows():
            print(f'  - ID: {row["id"]}')
            print(f'  - 类型: {row["type"]}')
            print(f'  - 描述: {row["description"][:100]}...')
